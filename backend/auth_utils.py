from passlib.context import Crypt<PERSON>ontext
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from datetime import datetime, timedelta
from typing import Optional, Union
import os
import random
import logging
from dotenv import load_dotenv
from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from database import get_async_session
from models.user_model import User

load_dotenv()

# Setup logger
logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token", auto_error=False)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash."""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """Verify JWT token and return payload."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

def generate_verification_code() -> str:
    """Generate a 6-digit verification code."""
    return ''.join([str(random.randint(0, 9)) for _ in range(6)])

def send_verification_email(email: str, code: str) -> bool:
    """Send verification email with code."""
    try:
        # Get SMTP settings from environment variables
        smtp_server = os.getenv("SMTP_SERVER")
        smtp_port = int(os.getenv("SMTP_PORT", "25"))
        smtp_username = os.getenv("SMTP_USERNAME")
        smtp_password = os.getenv("SMTP_PASSWORD")
        smtp_sender_name = os.getenv("SMTP_SENDER_NAME", "SteelNet")
        smtp_domain = os.getenv("SMTP_DOMAIN", "steelnet.ai")
        smtp_hostname = os.getenv("SMTP_HOSTNAME", "unit-converter.steelnet.ai")

        # Debug information
        logger.info(f"SMTP Configuration - Server: {smtp_server}, Port: {smtp_port}, Username: {smtp_username}")

        if not all([smtp_server, smtp_username, smtp_password]):
            logger.warning("SMTP settings not configured. Email verification will not be sent.")
            logger.info(f"Verification code for {email}: {code}")
            return True  # Return success in development to allow testing

        # Check if this is a QQ email address (needs special handling)
        is_qq_email = '@qq.com' in email.lower()
        logger.info(f"Sending to {'QQ email' if is_qq_email else 'standard email'}: {email}")

        # Load email template
        template_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "backend", "templates", "email_verification.html"
        )

        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                html_template = f.read()
        except FileNotFoundError:
            logger.warning(f"Email template not found at {template_path}, using fallback template")
            # Fallback template
            html_template = """
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; }
                    .container { padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; }
                    .header { text-align: center; padding-bottom: 10px; border-bottom: 1px solid #eee; }
                    .code { font-size: 24px; font-weight: bold; padding: 10px; text-align: center;
                            background-color: #f5f5f5; margin: 20px 0; letter-spacing: 5px; }
                    .footer { font-size: 12px; color: #777; margin-top: 30px; text-align: center; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>SteelNet</h1>
                        <p>Steel Industry Intelligent Assistant</p>
                    </div>
                    <div class="content">
                        <h2>Email Verification</h2>
                        <p>Hello!</p>
                        <p>Thank you for registering with SteelNet. To complete your registration, please use the following verification code:</p>

                        <div class="code">{{code}}</div>

                        <p>This verification code will expire in <strong>30 minutes</strong>.</p>

                        <p>If you did not request this verification code, please ignore this email.</p>

                        <p>After successful registration, you will be able to use our unit conversion, table generation, and consulting features to support your steel industry business.</p>

                        <p>Best regards,<br>The SteelNet Team</p>
                    </div>
                    <div class="footer">
                        <p>This email was sent automatically. Please do not reply directly.</p>
                        <p>&copy; {{year}} SteelNet. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """

        # Replace template variables
        current_year = datetime.utcnow().year
        html_content = html_template.replace("{{code}}", code).replace("{{year}}", str(current_year))

        # Create message container with improved formatting
        from email.mime.multipart import MIMEMultipart
        from email.utils import formataddr, make_msgid, formatdate

        msg = MIMEMultipart('alternative')

        # Set proper headers for better deliverability
        msg['Message-ID'] = make_msgid(domain=smtp_domain)
        msg['Date'] = formatdate(localtime=True)

        # For QQ email, use very simple subject and no special characters
        if is_qq_email:
            msg['Subject'] = f"Your SteelNet Code: {code}"
        else:
            msg['Subject'] = "SteelNet Verification Code"

        # Format the From header properly
        msg['From'] = formataddr((smtp_sender_name, smtp_username))
        msg['To'] = email
        msg['Reply-To'] = smtp_username

        # Add additional headers for better deliverability
        msg.add_header('X-Mailer', 'SteelNet Mailer')
        msg.add_header('X-Priority', '1')  # High priority

        # Add plain text alternative for better deliverability
        text_content = f"""
        SteelNet - Email Verification

        Thank you for registering with SteelNet.
        To complete your registration, please use this verification code: {code}

        This code will expire in 30 minutes.

        If you did not request this verification code, please ignore this email.

        Best regards,
        The SteelNet Team
        """

        # Attach plain text and HTML content
        msg.attach(MIMEText(text_content, 'plain', 'utf-8'))
        msg.attach(MIMEText(html_content, 'html', 'utf-8'))

        # Send the message with correct SMTP method
        logger.info(f"Attempting to connect to SMTP server: {smtp_server}:{smtp_port}")

        # Check if using SSL or TLS based on port
        use_ssl = smtp_port == 465

        if use_ssl:
            logger.info("Using SSL connection for SMTP")
            server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        else:
            logger.info("Using standard connection with STARTTLS")
            server = smtplib.SMTP(smtp_server, smtp_port)

            # Set the hostname for the EHLO command (helps with some email providers)
            server.local_hostname = smtp_hostname

            # Send EHLO
            server.ehlo()
            # Start TLS
            server.starttls()
            # Send EHLO again after STARTTLS
            server.ehlo()

        logger.info(f"SMTP connection established, attempting login with username: {smtp_username}")
        server.login(smtp_username, smtp_password)

        logger.info(f"SMTP login successful, sending email to: {email}")
        server.send_message(msg)
        server.quit()

        logger.info(f"Verification email sent to {email}")
        return True
    except Exception as e:
        logger.error(f"Error sending verification email: {str(e)}")
        # Print stack trace for debugging
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")

        # If email sending fails, log the verification code so it can be manually provided if needed
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False

async def get_current_user(token: str = Depends(oauth2_scheme), db_session: AsyncSession = Depends(get_async_session)) -> User:
    """
    Get the current user from JWT token.
    Raises 401 if token is invalid or user not found.
    """
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    payload = verify_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    username: str = payload.get("sub")
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create a new session for this request
    from database import AsyncSessionLocal

    async with AsyncSessionLocal() as session:
        async with session.begin():
            # Get user from database
            result = await session.execute(select(User).where(User.username == username))
            user = result.scalars().first()

            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Inactive user",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # Update last login time
            user.last_login = datetime.utcnow()

            # Create a detached copy of the user object with all the attributes we need
            user_dict = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "company_name": user.company_name,
                "country": user.country,
                "is_active": user.is_active,
                "is_admin": user.is_admin,
                "last_login": user.last_login,
                "created_at": user.created_at
            }

    # Create a new User instance that's not bound to any session
    detached_user = User(**user_dict)
    return detached_user

async def get_current_user_optional(token: str = Depends(oauth2_scheme)) -> Optional[User]:
    """
    Get the current user from the JWT token if available.
    This dependency will return None if the token is invalid or not provided.
    """
    try:
        return await get_current_user(token)
    except HTTPException:
        return None

async def get_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Check if the current user is an admin and return the user if so.
    This dependency will raise an HTTPException if the user is not an admin.
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to access this resource",
        )
    return current_user
