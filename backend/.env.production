# Production Environment Configuration

# Database configuration - MySQL 8.0 RDS
RDS_HOSTNAME=rm-uf6ky293vc3i3l991no.mysql.rds.aliyuncs.com
RDS_PORT=3306
RDS_DB_NAME=unit_converter
RDS_USERNAME=unit
RDS_PASSWORD=dnBW6x$^53$3Bxn

# Database connection pool settings (optimized for MySQL 8.0 RDS)
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
SQL_ECHO=False    

# Security
SECRET_KEY=your-production-secret-key
CORS_ORIGINS=https://steelnet.ai,http://localhost:3000

# Email settings
SMTP_SERVER=smtp.qiye.aliyun.com
SMTP_PORT=25
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=STEELnet456456

# Email rate limiting (to avoid being flagged as spam)
EMAIL_RATE_LIMIT=10
EMAIL_RATE_LIMIT_PERIOD=3600

# Sentry error tracking
SENTRY_DSN=your-sentry-dsn

# LLM service
VOLCANO_ENGINE_API_KEY=9ed8bdbe-1fa4-4a97-b4ae-52843714fdca
VOLCANO_API_KEY=9ed8bdbe-1fa4-4a97-b4ae-52843714fdca
VOLCANO_ENGINE_ENDPOINT_ID=deepseek-v3-250324
VOLCANO_ENDPOINT_ID=deepseek-v3-250324
VOLCANO_ENGINE_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
VOLCANO_ENGINE_TIMEOUT=300
VOLCANO_ENGINE_PROMPT_TEMPLATE=llm_prompt.txt