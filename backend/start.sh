#!/bin/bash

# <PERSON>ript to start the backend application
# This script fixes import issues and then starts the application

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Change to the script directory
cd "$SCRIPT_DIR" || exit

# Fix import issues
echo "Fixing import issues..."
python fix_imports.py

# Install missing dependencies
echo "Installing missing dependencies..."
python install_missing_deps.py

# Start the application
echo "Starting the application..."
python main.py
