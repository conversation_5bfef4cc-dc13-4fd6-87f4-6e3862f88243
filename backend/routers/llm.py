"""
LLM Router - Handles API endpoints for LLM operations

This router provides endpoints for interacting with the LLM service,
including unit conversion, general queries, and administrative functions.
"""
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.security import OAuth2PasswordBearer
from pydantic import BaseModel
import logging
import os
import uuid
from datetime import datetime

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from database import get_async_session
from auth_utils import get_current_user_optional, get_admin_user
from llm.service import llm_service
from llm.volcano_engine import volcano_engine
from models.user_model import User
from models.conversion_model import Conversion
from models.chat_history import ChatHistory
from utils.oss_utils import oss_manager

# Use /api/llm prefix to match frontend expectations
router = APIRouter(prefix="/api/llm", tags=["llm"])
logger = logging.getLogger(__name__)

class QueryRequest(BaseModel):
    text: str
    unit_system: str = "metric"
    model: str = "deepseek-v3-250324"
    function: str = "general"  # general, conversion, or table

class QueryResponse(BaseModel):
    result: Dict[str, Any]
    message: str

class LLMStatusResponse(BaseModel):
    """Response model for LLM status endpoint"""
    is_configured: bool
    provider: str
    model: str
    available_functions: List[Dict[str, Any]]

@router.post("/", response_model=QueryResponse)
async def process_query(
    request: QueryRequest, 
    current_user: Optional[User] = Depends(get_current_user_optional),
    session: AsyncSession = Depends(get_async_session)
):
    """
    Process a query using the appropriate LLM function (general query, unit conversion, or table creation).
    """
    try:
        logger.info(f"LLM query received: text='{request.text}', function={request.function}, unit_system={request.unit_system}")
        
        # Parse function parameter - can be comma-separated list of functions
        functions = request.function.split(',')
        table_mode = 'table' in functions
        
        # Default to general if no valid functions specified
        if not functions or all(f.strip() == '' for f in functions):
            function_type = 'general'
        # If conversion or table is included, prioritize those
        elif any(f in ['conversion', 'table'] for f in functions):
            function_type = 'conversion'
        else:
            function_type = 'general'
        
        logger.info(f"Using function_type={function_type}, table_mode={table_mode}")
        
        result = await llm_service.convert_units(
            text=request.text,
            unit_system=request.unit_system,
            model=request.model,
            function=function_type,
            table_mode=table_mode
        )
        
        # Store the query in the database if user is authenticated
        if current_user:
            try:
                # Store in conversions table
                conversion = Conversion(
                    user_id=current_user.id,
                    input_text=request.text,
                    from_unit="various",
                    to_unit="various",
                    input_value=0.0,
                    output_value=0.0,
                    unit_system=request.unit_system,
                    model=request.model,
                    function=request.function
                )
                session.add(conversion)
                
                # Store chat history in OSS
                try:
                    # Get or create session ID
                    session_id = str(uuid.uuid4())
                    
                    # Format user message
                    user_message = {
                        "role": "user",
                        "content": request.text,
                        "function": request.function,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # Format assistant message
                    assistant_content = ""
                    if function_type == "conversion":
                        assistant_content = result["result"].get("converted_text", "")
                    else:
                        assistant_content = result["result"].get("text", "")
                    
                    assistant_message = {
                        "role": "assistant",
                        "content": assistant_content,
                        "function": request.function,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # Save to OSS
                    messages = [user_message, assistant_message]
                    object_key = oss_manager.save_chat_history(
                        user_id=current_user.id,
                        session_id=session_id,
                        messages=messages
                    )
                    
                    if object_key:
                        # Create chat history record
                        chat_history = ChatHistory(
                            user_id=current_user.id,
                            session_id=session_id,
                            title=request.text[:50] + "..." if len(request.text) > 50 else request.text,
                            oss_object_key=object_key,
                            message_count=2
                        )
                        session.add(chat_history)
                except Exception as e:
                    logger.error(f"Error saving chat history to OSS: {e}")
                    # Continue even if OSS storage fails
                
                await session.commit()
            except Exception as e:
                logger.error(f"Error storing query: {e}")
                # Continue even if storage fails
        
        return result
    except Exception as e:
        logger.error(f"Error in query endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/functions")
async def get_available_functions():
    """
    Get the list of available functions that the API can perform.
    """
    try:
        logger.info("LLM functions endpoint called")
        functions = await llm_service.get_available_functions()
        logger.info(f"Returning functions: {functions}")
        return functions
    except Exception as e:
        logger.error(f"Error getting available functions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status", response_model=LLMStatusResponse)
async def get_llm_status(
    admin_user = Depends(get_admin_user)
):
    """
    Get the status of the LLM service.
    This endpoint is only accessible to admin users.
    """
    try:
        functions = await llm_service.get_available_functions()
        return LLMStatusResponse(
            is_configured=bool(volcano_engine.api_key),
            provider="火山引擎 (Volcano Engine)",
            model=volcano_engine.endpoint_id,
            available_functions=functions
        )
    except Exception as e:
        logger.error(f"Error getting LLM status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history", response_model=list[Dict[str, Any]])
async def get_query_history(
    current_user: User = Depends(get_current_user_optional),
    session: AsyncSession = Depends(get_async_session),
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0)
):
    """
    Get the query history for the authenticated user.
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    try:
        # Query for the most recent queries by this user
        result = await session.execute(
            "SELECT id, input_text, from_unit, to_unit, input_value, output_value, unit_system, function, created_at "
            "FROM conversions "
            "WHERE user_id = :user_id "
            "ORDER BY created_at DESC "
            "LIMIT :limit OFFSET :offset",
            {"user_id": current_user.id, "limit": limit, "offset": offset}
        )
        
        conversions = []
        for row in result:
            conversions.append({
                "id": row.id,
                "input_text": row.input_text,
                "from_unit": row.from_unit,
                "to_unit": row.to_unit,
                "input_value": row.input_value,
                "output_value": row.output_value,
                "unit_system": row.unit_system,
                "function": row.function,
                "created_at": row.created_at.isoformat()
            })
        
        return conversions
    except Exception as e:
        logger.error(f"Error in get_query_history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 