from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, EmailStr
import logging
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_async_session
from models.user_model import User
from auth_utils import get_current_user_optional

router = APIRouter(prefix="/api/email", tags=["email"])
logger = logging.getLogger(__name__)

class EmailRequest(BaseModel):
    email: EmailStr
    data: Dict[str, Any]
    type: str  # 'conversion' or 'table'

class EmailResponse(BaseModel):
    success: bool
    message: str

def send_email(to_email: str, subject: str, html_content: str) -> bool:
    """Send an email using SMTP"""
    try:
        # Get SMTP settings from environment variables
        smtp_server = os.getenv("SMTP_SERVER")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        smtp_username = os.getenv("SMTP_USERNAME")
        smtp_password = os.getenv("SMTP_PASSWORD")
        
        # Create message container
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = f"钢铁智联 SteelNet <{smtp_username}>"
        msg['To'] = to_email
        
        # Attach HTML content
        msg.attach(MIMEText(html_content, 'html'))
        
        # Send the message
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            
        return True
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        return False

def create_conversion_email_content(data: Dict[str, Any]) -> str:
    """Create HTML content for conversion email"""
    from_unit = data.get("from", "")
    to_unit = data.get("to", "")
    value = data.get("value", 0)
    converted_value = data.get("converted_value", 0)
    
    html = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
            .header {{ background: #10A37F; color: white; padding: 10px 20px; border-radius: 5px 5px 0 0; }}
            .content {{ padding: 20px; border: 1px solid #ddd; border-radius: 0 0 5px 5px; }}
            .result {{ background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            .conversion {{ font-size: 18px; font-weight: bold; text-align: center; }}
            .unit-label {{ display: inline-block; background: #5436DA; color: white; padding: 3px 8px; 
                border-radius: 3px; font-size: 12px; margin-right: 5px; }}
            .footer {{ margin-top: 30px; font-size: 12px; color: #777; text-align: center; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>钢铁智联 | SteelNet</h2>
            </div>
            <div class="content">
                <p>您好！</p>
                <p>以下是您在钢铁智联平台上的单位转换结果：</p>
                
                <div class="result">
                    <div class="conversion">
                        <span class="unit-label">{from_unit}</span> {value} 
                        <span style="margin: 0 10px;">→</span> 
                        <span class="unit-label">{to_unit}</span> {converted_value}
                    </div>
                </div>
                
                <p>感谢您使用钢铁智联的单位转换服务。如有任何问题，请随时联系我们。</p>
                
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿直接回复。</p>
                    <p>© {datetime.now().year} 钢铁智联 | SteelNet</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    return html

def create_table_email_content(data: Dict[str, Any]) -> str:
    """Create HTML content for table email"""
    steel_type = data.get("steelType", "Carbon Steel")
    length = data.get("length", {"value": 0, "unit": "mm"})
    width = data.get("width", {"value": 0, "unit": "mm"})
    thickness = data.get("thickness", {"value": 0, "unit": "mm"})
    weight = data.get("weight", {"value": 0, "unit": "kg"})
    
    html = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
            .header {{ background: #10A37F; color: white; padding: 10px 20px; border-radius: 5px 5px 0 0; }}
            .content {{ padding: 20px; border: 1px solid #ddd; border-radius: 0 0 5px 5px; }}
            .result {{ background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            .footer {{ margin-top: 30px; font-size: 12px; color: #777; text-align: center; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>钢铁智联 | SteelNet</h2>
            </div>
            <div class="content">
                <p>您好！</p>
                <p>以下是您在钢铁智联平台上的钢材数据：</p>
                
                <div class="result">
                    <table>
                        <tr>
                            <th>Steel Type</th>
                            <th>Length ({length["unit"]})</th>
                            <th>Width ({width["unit"]})</th>
                            <th>Thickness ({thickness["unit"]})</th>
                            <th>Weight ({weight["unit"]})</th>
                        </tr>
                        <tr>
                            <td>{steel_type}</td>
                            <td>{length["value"]:.2f}</td>
                            <td>{width["value"]:.2f}</td>
                            <td>{thickness["value"]:.2f}</td>
                            <td>{weight["value"]:.2f}</td>
                        </tr>
                    </table>
                </div>
                
                <p>感谢您使用钢铁智联的单位转换服务。如有任何问题，请随时联系我们。</p>
                
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿直接回复。</p>
                    <p>© {datetime.now().year} 钢铁智联 | SteelNet</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    return html

@router.post("/send", response_model=EmailResponse)
async def send_result_email(
    request: EmailRequest,
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """
    Send conversion result to email
    """
    try:
        # Determine email subject and content based on type
        if request.type == "conversion":
            subject = "钢铁智联 - 单位转换结果"
            html_content = create_conversion_email_content(request.data)
        elif request.type == "table":
            subject = "钢铁智联 - 钢材数据表格"
            html_content = create_table_email_content(request.data)
        else:
            raise HTTPException(status_code=400, detail="Invalid email type")
            
        # Send the email
        success = send_email(request.email, subject, html_content)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to send email")
            
        return {
            "success": True,
            "message": "Email sent successfully"
        }
    except Exception as e:
        logger.error(f"Error in send_result_email: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 