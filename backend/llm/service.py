"""
LLM Service Module

This module provides high-level services built on top of the LLM connectors.
It handles use-case specific formatting and processing of LLM responses.
"""
import json
import logging
import os
from typing import Dict, Any, Optional, List, AsyncGenerator

from fastapi import HTTPException

from .volcano_engine import volcano_engine
from .config import llm_config

logger = logging.getLogger(__name__)

class LLMService:
    """
    Service for performing unit conversions using DeepSeek R1 model on 火山引擎.
    This class strictly relies on the LLM model for all conversion logic,
    with no hardcoded conversion rules.
    """

    def __init__(self):
        # Load prompt templates
        self.prompt_templates = {
            "conversion": self._load_prompt_template(llm_config.prompt_template_path),
            "general": self._load_prompt_template("llm/common_prompt.txt")
        }
        logger.info(f"Loaded {len(self.prompt_templates)} prompt templates")

    def _load_prompt_template(self, template_path: str) -> str:
        """Load a prompt template from the specified file."""
        try:
            # First try to load from the backend root directory
            backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            full_template_path = os.path.join(backend_dir, template_path)

            if not os.path.exists(full_template_path):
                # Try to load from the current directory
                full_template_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), template_path)

            if not os.path.exists(full_template_path):
                # Try to load from the parent directory (backend)
                full_template_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                              os.path.basename(template_path))

            if not os.path.exists(full_template_path):
                # If still not found, log error as we must use the standard template
                logger.error(f"Prompt template file not found at {full_template_path}")
                raise FileNotFoundError(f"Required prompt template file not found: {template_path}")

            with open(full_template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
                logger.info(f"Successfully loaded prompt template from {full_template_path}")
                return template_content
        except Exception as e:
            logger.error(f"Error loading prompt template: {e}")
            raise

    async def convert_units_stream(self, text: str, unit_system: str = "metric", model: str = None,
                                 function: str = "conversion", table_mode: bool = False) -> AsyncGenerator[str, None]:
        """
        Stream unit conversion or general queries using the 火山引擎 DeepSeek model.

        Args:
            text: The text containing the user request from the frontend
            unit_system: The preferred unit system (metric or imperial) for conversions
            model: The model to use, defaults to the one specified in the config (endpoint_id)
            function: Which function to use - "conversion" or "general"
            table_mode: Whether to create a table output (for conversions)

        Yields:
            Streaming response chunks as formatted JSON strings
        """
        # Check if API key is configured
        if not volcano_engine.api_key:
            logger.warning("LLM API key is not configured. Using mock response.")
            # Return a mock streaming response for testing
            mock_response = {
                "type": "content",
                "content": f"Mock streaming response for '{text}' (API key not configured)",
                "function": function,
                "unit_system": unit_system,
                "table_mode": table_mode
            }
            yield json.dumps(mock_response, ensure_ascii=False)
            return

        # Use the model from parameter or fall back to the configured endpoint_id
        model_to_use = model if model else llm_config.endpoint_id
        logger.info(f"Processing streaming request with function: {function}, table mode: {table_mode}, model: {model_to_use}")

        # Choose the appropriate prompt template
        if function == "conversion":
            # Set direction based on unit_system
            direction = "M" if unit_system.lower() == "metric" else "I"
            logger.info(f"Using conversion function with direction: {direction}")

            # Get the conversion prompt template
            system_prompt = self.prompt_templates["conversion"]

            # Replace template variables in the system prompt
            system_prompt = system_prompt.replace("{{DIRECTION}}", direction)
            system_prompt = system_prompt.replace("{{INPUT}}", text)

            # Mention that table mode is enabled if requested
            if table_mode:
                # Add a hint for table creation in the user prompt
                user_prompt = f"请将以下内容转换并制成表格: {text}"
                logger.info("Table mode enabled for this conversion")
            else:
                user_prompt = text
        else:
            # Use the general prompt
            system_prompt = self.prompt_templates["general"]
            user_prompt = text
            logger.info("Using general query function")

        logger.info(f"Using prompt template: {function}")

        try:
            # Send the prompt to DeepSeek R1 model through 火山引擎 with streaming
            logger.info("Starting streaming request to 火山引擎 API")
            
            accumulated_content = ""
            
            async for chunk_json in volcano_engine.generate_response_stream(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=0,
                max_tokens=2000,
                model=model
            ):
                try:
                    chunk_data = json.loads(chunk_json)
                    
                    # Extract content from the streaming chunk
                    if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                        choice = chunk_data["choices"][0]
                        
                        # Handle delta content for streaming
                        if "delta" in choice and "content" in choice["delta"]:
                            content_chunk = choice["delta"]["content"]
                            accumulated_content += content_chunk
                            
                            # Send the chunk to the frontend
                            response_chunk = {
                                "type": "content",
                                "content": content_chunk,
                                "accumulated_content": accumulated_content,
                                "function": function,
                                "unit_system": unit_system,
                                "table_mode": table_mode,
                                "model": model_to_use
                            }
                            yield json.dumps(response_chunk, ensure_ascii=False)
                        
                        # Handle completion
                        elif "finish_reason" in choice and choice["finish_reason"]:
                            # Send final completion message
                            if function == "conversion":
                                # Extract content between <converted_content> tags for conversion function
                                converted_content = volcano_engine.extract_converted_content(accumulated_content)
                                
                                if not converted_content or converted_content == accumulated_content:
                                    logger.warning("No <converted_content> tags found in response. Using full response.")
                                    converted_content = accumulated_content
                                
                                # Check if the response contains a table format
                                has_table = "| " in converted_content and "\n|" in converted_content
                                
                                completion_response = {
                                    "type": "completion",
                                    "result": {
                                        "converted_text": converted_content,
                                        "original_text": text,
                                        "unit_system": unit_system,
                                        "model": model_to_use,
                                        "from": text,
                                        "to": converted_content,
                                        "value": 1,
                                        "converted_value": 1,
                                        "hasTable": has_table,
                                        "function": function,
                                        "table_mode": table_mode
                                    },
                                    "message": "Unit conversion completed successfully"
                                }
                            else:
                                completion_response = {
                                    "type": "completion",
                                    "result": {
                                        "text": accumulated_content,
                                        "original_text": text,
                                        "model": model_to_use,
                                        "function": function
                                    },
                                    "message": "Query processed successfully"
                                }
                            
                            yield json.dumps(completion_response, ensure_ascii=False)
                            break
                            
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse streaming chunk: {e}")
                    continue

        except Exception as e:
            logger.error(f"LLM streaming processing error: {str(e)}")
            error_response = {
                "type": "error",
                "error": f"An error occurred during processing: {str(e)}",
                "function": function
            }
            yield json.dumps(error_response, ensure_ascii=False)

    async def convert_units(self, text: str, unit_system: str = "metric", model: str = None,
                          function: str = "conversion", table_mode: bool = False) -> Dict[str, Any]:
        """
        Use the 火山引擎 DeepSeek model for unit conversion or general queries.

        Args:
            text: The text containing the user request from the frontend
            unit_system: The preferred unit system (metric or imperial) for conversions
            model: The model to use, defaults to the one specified in the config (endpoint_id)
            function: Which function to use - "conversion" or "general"
            table_mode: Whether to create a table output (for conversions)

        Returns:
            Dictionary containing the processed result from the LLM
        """
        # Check if API key is configured
        if not volcano_engine.api_key:
            logger.warning("LLM API key is not configured. Using mock response.")
            # Use the model from parameter or fall back to the configured endpoint_id
            model_to_use = model if model else llm_config.endpoint_id

            # Return a mock response for testing
            if function == "conversion":
                return {
                    "result": {
                        "converted_text": f"Mock conversion response for '{text}' (API key not configured)",
                        "original_text": text,
                        "unit_system": unit_system,
                        "model": model_to_use,
                        "function": function,
                        "from": text,
                        "to": f"Mock conversion for '{text}'",
                        "value": 1,
                        "converted_value": 1,
                        "hasTable": False,
                        "table_mode": table_mode
                    },
                    "message": "Mock response (API key not configured)"
                }
            else:
                return {
                    "result": {
                        "text": f"Mock response for '{text}' (API key not configured)",
                        "original_text": text,
                        "model": model_to_use,
                        "function": function
                    },
                    "message": "Mock response (API key not configured)"
                }

        # Use the model from parameter or fall back to the configured endpoint_id
        model_to_use = model if model else llm_config.endpoint_id
        logger.info(f"Processing request with function: {function}, table mode: {table_mode}, model: {model_to_use}")

        # Choose the appropriate prompt template
        if function == "conversion":
            # Set direction based on unit_system
            direction = "M" if unit_system.lower() == "metric" else "I"
            logger.info(f"Using conversion function with direction: {direction}")

            # Get the conversion prompt template
            system_prompt = self.prompt_templates["conversion"]

            # Replace template variables in the system prompt
            system_prompt = system_prompt.replace("{{DIRECTION}}", direction)
            system_prompt = system_prompt.replace("{{INPUT}}", text)

            # Mention that table mode is enabled if requested
            if table_mode:
                # Add a hint for table creation in the user prompt
                user_prompt = f"请将以下内容转换并制成表格: {text}"
                logger.info("Table mode enabled for this conversion")
            else:
                user_prompt = text
        else:
            # Use the general prompt
            system_prompt = self.prompt_templates["general"]
            user_prompt = text
            logger.info("Using general query function")

        logger.info(f"Using prompt template: {function}")

        try:
            # Send the prompt to DeepSeek R1 model through 火山引擎
            logger.info("Sending request to 火山引擎 API")
            response_data = await volcano_engine.generate_response(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=0,
                max_tokens=2000,
                model=model
            )

            # Extract the model's response
            if "choices" in response_data and len(response_data["choices"]) > 0:
                choice = response_data["choices"][0]

                # Extract the content from the message object
                if "message" in choice and "content" in choice["message"]:
                    ai_message = choice["message"]["content"]
                else:
                    # Fall back to delta content if available (for streaming)
                    ai_message = choice.get("delta", {}).get("content", "")

                logger.info(f"Raw LLM response: {ai_message}")

                if function == "conversion":
                    # Extract content between <converted_content> tags for conversion function
                    converted_content = volcano_engine.extract_converted_content(ai_message)

                    if not converted_content or converted_content == ai_message:
                        logger.warning("No <converted_content> tags found in response. Using full response.")
                        converted_content = ai_message
                    else:
                        logger.info(f"Extracted converted content: {converted_content}")

                    # Check if the response contains a table format
                    has_table = "| " in converted_content and "\n|" in converted_content
                    if has_table:
                        logger.info("Table format detected in response.")

                    # Use the model that was actually used in the request
                    model_used = model if model else llm_config.endpoint_id

                    # Format the conversion response
                    return {
                        "result": {
                            "converted_text": converted_content,
                            "original_text": text,
                            "unit_system": unit_system,
                            "model": model_used,
                            "from": text,
                            "to": converted_content,
                            "value": 1,
                            "converted_value": 1,
                            "hasTable": has_table,
                            "function": function,
                            "table_mode": table_mode
                        },
                        "message": "Unit conversion completed successfully"
                    }
                else:
                    # Use the model that was actually used in the request
                    model_used = model if model else llm_config.endpoint_id

                    # Format the general response
                    return {
                        "result": {
                            "text": ai_message,
                            "original_text": text,
                            "model": model_used,
                            "function": function
                        },
                        "message": "Query processed successfully"
                    }
            else:
                raise HTTPException(status_code=500, detail="Invalid response from DeepSeek R1 API")

        except Exception as e:
            logger.error(f"LLM processing error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"An error occurred during processing: {str(e)}")

    async def get_available_functions(self) -> List[Dict[str, Any]]:
        """
        Get the list of available functions that the API can perform.

        Returns:
            List of function objects with their details
        """
        functions = [
            {
                "id": "general",
                "name": "钢铁行业咨询",
                "description": "一般钢铁行业知识查询和咨询",
                "icon": "chat"
            },
            {
                "id": "conversion",
                "name": "单位转换",
                "description": "英制/公制单位转换",
                "icon": "calculate"
            },
            {
                "id": "table",
                "name": "制表功能",
                "description": "单位转换并生成表格",
                "icon": "table_chart"
            }
        ]

        return functions

# Create a singleton instance
llm_service = LLMService()