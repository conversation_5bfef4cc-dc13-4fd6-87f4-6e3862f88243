import re
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass

@dataclass
class UnitValue:
    value: float
    unit: str

@dataclass
class SteelDimensions:
    length: Optional[UnitValue] = None
    width: Optional[UnitValue] = None
    thickness: Optional[UnitValue] = None
    weight: Optional[UnitValue] = None
    steel_type: Optional[str] = None

# Conversion factors
IMPERIAL_TO_METRIC = {
    'inch': 25.4,  # to mm
    'inches': 25.4,
    'in': 25.4,
    '"': 25.4,
    'foot': 304.8,  # to mm
    'feet': 304.8,
    'ft': 304.8,
    "'": 304.8,
    'pound': 0.453592,  # to kg
    'pounds': 0.453592,
    'lbs': 0.453592,
    'lb': 0.453592,
}

METRIC_TO_IMPERIAL = {
    'mm': 1/25.4,  # to inches
    'millimeter': 1/25.4,
    'millimeters': 1/25.4,
    'cm': 1/2.54,  # to inches
    'centimeter': 1/2.54,
    'centimeters': 1/2.54,
    'm': 3.28084,  # to feet
    'meter': 3.28084,
    'meters': 3.28084,
    'kg': 2.20462,  # to pounds
    'kilogram': 2.20462,
    'kilograms': 2.20462,
}

# Gauge to mm conversion (approximate)
GAUGE_TO_MM = {
    '10': 3.4,
    '11': 3.0,
    '12': 2.7,
    '13': 2.3,
    '14': 1.9,
    '15': 1.7,
    '16': 1.5,
    '17': 1.4,
    '18': 1.2,
    '19': 1.0,
    '20': 0.9,
}

def extract_number_and_unit(text: str) -> Tuple[Optional[float], Optional[str]]:
    """Extract number and unit from text."""
    # Pattern for number followed by unit
    pattern = r'([-+]?\d*\.?\d+)\s*([a-zA-Z\'\"]+|\"|\'|GA|ga|gauge)'
    match = re.search(pattern, text)
    if match:
        value = float(match.group(1))
        unit = match.group(2).lower().strip()
        return value, unit
    return None, None

def convert_gauge_to_mm(gauge: str) -> float:
    """Convert gauge to millimeters."""
    return GAUGE_TO_MM.get(gauge.replace('GA', '').replace('ga', '').strip(), 0)

def convert_to_metric(value: float, unit: str) -> UnitValue:
    """Convert imperial units to metric."""
    if unit in IMPERIAL_TO_METRIC:
        factor = IMPERIAL_TO_METRIC[unit]
        if 'foot' in unit or 'ft' in unit or unit == "'":
            return UnitValue(value * factor, 'mm')
        elif 'inch' in unit or 'in' in unit or unit == '"':
            return UnitValue(value * factor, 'mm')
        elif 'pound' in unit or 'lb' in unit:
            return UnitValue(value * factor, 'kg')
    elif unit.endswith(('ga', 'gauge', 'GA')):
        return UnitValue(convert_gauge_to_mm(unit), 'mm')
    return UnitValue(value, unit)

def convert_to_imperial(value: float, unit: str) -> UnitValue:
    """Convert metric units to imperial."""
    if unit in METRIC_TO_IMPERIAL:
        factor = METRIC_TO_IMPERIAL[unit]
        if unit in ['m', 'meter', 'meters']:
            return UnitValue(value * factor, 'ft')
        elif unit in ['mm', 'millimeter', 'millimeters', 'cm', 'centimeter', 'centimeters']:
            return UnitValue(value * factor, 'in')
        elif unit in ['kg', 'kilogram', 'kilograms']:
            return UnitValue(value * factor, 'lb')
    return UnitValue(value, unit)

def parse_steel_dimensions(text: str) -> SteelDimensions:
    """Parse steel dimensions from text input."""
    result = SteelDimensions()
    
    # Extract steel type
    steel_types = ['carbon steel', 'stainless steel', 'alloy steel', 'tool steel']
    for steel_type in steel_types:
        if steel_type in text.lower():
            result.steel_type = steel_type
            break
    
    # Extract dimensions
    text_lower = text.lower()
    
    # Length
    length_match = re.search(r'length[:\s]+([^,\n]+)', text_lower)
    if length_match:
        value, unit = extract_number_and_unit(length_match.group(1))
        if value and unit:
            result.length = UnitValue(value, unit)
    
    # Width
    width_match = re.search(r'width[:\s]+([^,\n]+)', text_lower)
    if width_match:
        value, unit = extract_number_and_unit(width_match.group(1))
        if value and unit:
            result.width = UnitValue(value, unit)
    
    # Thickness
    thickness_match = re.search(r'(thickness|gauge)[:\s]+([^,\n]+)', text_lower)
    if thickness_match:
        value, unit = extract_number_and_unit(thickness_match.group(2))
        if value and unit:
            result.thickness = UnitValue(value, unit)
    
    # Weight
    weight_match = re.search(r'weight[:\s]+([^,\n]+)', text_lower)
    if weight_match:
        value, unit = extract_number_and_unit(weight_match.group(1))
        if value and unit:
            result.weight = UnitValue(value, unit)
    
    return result

def convert_dimensions(dimensions: SteelDimensions, target_system: str) -> SteelDimensions:
    """Convert dimensions to target unit system."""
    result = SteelDimensions(steel_type=dimensions.steel_type)
    
    convert_func = convert_to_metric if target_system == 'metric' else convert_to_imperial
    
    if dimensions.length:
        result.length = convert_func(dimensions.length.value, dimensions.length.unit)
    if dimensions.width:
        result.width = convert_func(dimensions.width.value, dimensions.width.unit)
    if dimensions.thickness:
        result.thickness = convert_func(dimensions.thickness.value, dimensions.thickness.unit)
    if dimensions.weight:
        result.weight = convert_func(dimensions.weight.value, dimensions.weight.unit)
    
    return result
