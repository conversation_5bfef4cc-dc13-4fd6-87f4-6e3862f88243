<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Parsing Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .test-content { background: #f5f5f5; padding: 10px; white-space: pre-wrap; font-family: monospace; }
        .result { background: #e8f5e8; padding: 10px; margin-top: 10px; }
        .error { background: #ffe8e8; padding: 10px; margin-top: 10px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Robust Table Parsing Tests</h1>
    
    <div class="test-case">
        <h3>Test 1: Standard Markdown Table</h3>
        <div class="test-content">| Product Type | Thickness (mm) | Width (mm) | Weight (kg) |
|--------------|----------------|------------|-------------|
| T304 2B      | 0.23           | 914.4      | 9071.85     |
| T304 2B      | 0.38           | 914.4      | 18143.7     |</div>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test 2: Table with Missing Separators</h3>
        <div class="test-content">Product Type	Thickness (mm)	Width (mm)	Weight (kg)
T304 2B	0.46	15.61	3175.14
T304 2B	0.46	16.21	4535.92
T304 2B	0.89	13.72	907.18</div>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test 3: Malformed Table (Missing Headers)</h3>
        <div class="test-content">| 0.23 | 914.4 | 9071.85 |
| 0.38 | 914.4 | 18143.7 |</div>
        <div id="result3" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test 4: Empty/Invalid Content</h3>
        <div class="test-content"></div>
        <div id="result4" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test 5: Table with Converted Content Tags</h3>
        <div class="test-content"><converted_content>
| Product Type | Thickness (mm) | Width (mm) | Weight (kg) |
|--------------|----------------|------------|-------------|
| S/S 430 BA   | 0.38           | 59.52      | 3260.53     |
| S/S 430 BA   | 0.38           | 61.11      | 3656.73     |
</converted_content></div>
        <div id="result5" class="result"></div>
    </div>

    <script>
        // Simulate the table parsing functions (simplified versions for testing)
        function parseMarkdownTable(markdownTable) {
            try {
                if (!markdownTable || typeof markdownTable !== 'string') {
                    return { headers: [], rows: [], isValid: false, error: 'Empty or invalid table content' };
                }

                const lines = markdownTable.trim().split('\n').filter(line => line.trim().length > 0);

                if (lines.length < 2) {
                    return { headers: [], rows: [], isValid: false, error: 'Insufficient table rows' };
                }

                // More flexible header parsing
                const headerLine = lines[0].trim();
                let headers = [];
                
                if (headerLine.includes('|')) {
                    const cleanHeader = headerLine.replace(/^\||\|$/g, '');
                    headers = cleanHeader.split('|').map(h => h.trim()).filter(h => h.length > 0);
                } else {
                    headers = headerLine.split(/\s{2,}|\t/).map(h => h.trim()).filter(h => h.length > 0);
                }

                if (headers.length === 0) {
                    return { headers: [], rows: [], isValid: false, error: 'No valid headers found' };
                }

                // Find separator line
                let separatorIndex = -1;
                for (let i = 1; i < Math.min(3, lines.length); i++) {
                    const line = lines[i].trim();
                    if (line.match(/^[\|\s]*[-:]+[\|\s-:]*$/)) {
                        separatorIndex = i;
                        break;
                    }
                }

                let startRowIndex = separatorIndex !== -1 ? separatorIndex + 1 : 1;

                // Parse rows
                const rows = [];
                for (let i = startRowIndex; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line || line.startsWith('<!--') || line.match(/^[\|\s-:]*$/)) continue;

                    let cells = [];
                    
                    if (line.includes('|')) {
                        const cleanLine = line.replace(/^\||\|$/g, '');
                        cells = cleanLine.split('|').map(cell => cell.trim());
                    } else {
                        cells = line.split(/\s{2,}|\t/).map(cell => cell.trim()).filter(cell => cell.length > 0);
                    }

                    if (cells.some(cell => cell.length > 0)) {
                        const row = {};
                        headers.forEach((header, index) => {
                            row[header] = cells[index] || '';
                        });
                        rows.push(row);
                    }
                }

                const isValid = headers.length > 0 && rows.length > 0;
                return { 
                    headers, 
                    rows, 
                    isValid,
                    error: isValid ? undefined : 'No valid table data found'
                };

            } catch (error) {
                return { 
                    headers: [], 
                    rows: [], 
                    isValid: false, 
                    error: `Parsing error: ${error.message}`
                };
            }
        }

        function parseConvertedContent(content) {
            const convertedContentMatch = content.match(/<converted_content>([\s\S]*?)<\/converted_content>/);
            
            if (convertedContentMatch) {
                const tableContent = convertedContentMatch[1].trim();
                const cleanContent = content.replace(/<converted_content>[\s\S]*?<\/converted_content>/, '').trim();
                return {
                    hasTable: tableContent.includes('|') || tableContent.includes('\t'),
                    tableContent,
                    cleanContent
                };
            }
            
            return {
                hasTable: false,
                tableContent: '',
                cleanContent: content
            };
        }

        function createTable(parsedTable) {
            if (!parsedTable.isValid) {
                return `<div class="error">Error: ${parsedTable.error}</div>`;
            }

            let html = '<table>';
            html += '<thead><tr>';
            parsedTable.headers.forEach(header => {
                html += `<th>${header}</th>`;
            });
            html += '</tr></thead><tbody>';
            
            parsedTable.rows.forEach(row => {
                html += '<tr>';
                parsedTable.headers.forEach(header => {
                    html += `<td>${row[header] || ''}</td>`;
                });
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            return html;
        }

        // Test cases
        const tests = [
            {
                id: 'result1',
                content: `| Product Type | Thickness (mm) | Width (mm) | Weight (kg) |
|--------------|----------------|------------|-------------|
| T304 2B      | 0.23           | 914.4      | 9071.85     |
| T304 2B      | 0.38           | 914.4      | 18143.7     |`
            },
            {
                id: 'result2',
                content: `Product Type	Thickness (mm)	Width (mm)	Weight (kg)
T304 2B	0.46	15.61	3175.14
T304 2B	0.46	16.21	4535.92
T304 2B	0.89	13.72	907.18`
            },
            {
                id: 'result3',
                content: `| 0.23 | 914.4 | 9071.85 |
| 0.38 | 914.4 | 18143.7 |`
            },
            {
                id: 'result4',
                content: ``
            },
            {
                id: 'result5',
                content: `<converted_content>
| Product Type | Thickness (mm) | Width (mm) | Weight (kg) |
|--------------|----------------|------------|-------------|
| S/S 430 BA   | 0.38           | 59.52      | 3260.53     |
| S/S 430 BA   | 0.38           | 61.11      | 3656.73     |
</converted_content>`
            }
        ];

        // Run tests
        tests.forEach(test => {
            const resultDiv = document.getElementById(test.id);
            
            if (test.id === 'result5') {
                // Test converted content parsing
                const parsed = parseConvertedContent(test.content);
                if (parsed.hasTable) {
                    const tableResult = parseMarkdownTable(parsed.tableContent);
                    resultDiv.innerHTML = createTable(tableResult);
                } else {
                    resultDiv.innerHTML = '<div class="error">No table detected in converted content</div>';
                }
            } else {
                // Test direct table parsing
                const parsed = parseMarkdownTable(test.content);
                resultDiv.innerHTML = createTable(parsed);
                
                if (!parsed.isValid && test.content.trim()) {
                    resultDiv.innerHTML += `<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7;">
                        <strong>Fallback Display:</strong><br>
                        <pre style="margin: 5px 0; font-family: monospace; background: #f8f9fa; padding: 5px;">${test.content}</pre>
                    </div>`;
                }
            }
        });
    </script>
</body>
</html> 