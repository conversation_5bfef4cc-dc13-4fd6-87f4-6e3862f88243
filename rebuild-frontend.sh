#!/usr/bin/env bash

# Script to rebuild the frontend with fixed configuration

# Set script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
DIST_DIR="$FRONTEND_DIR/dist"

# Function to print colored output
print_info() {
    echo -e "\033[0;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[0;32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[0;33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $1"
}

# Change directory to frontend
cd "$FRONTEND_DIR" || exit

# Stop any running processes
print_info "Stopping any running processes..."
cd "$SCRIPT_DIR" && ./start-built.sh stop
cd "$FRONTEND_DIR" || exit

# Clean up the dist directory
print_info "Cleaning up the dist directory..."
rm -rf "$DIST_DIR"
mkdir -p "$DIST_DIR"

# Copy the fixed vite config
print_info "Using fixed vite configuration..."
cp "$FRONTEND_DIR/vite.config.fixed.js" "$FRONTEND_DIR/vite.config.js"

# Install dependencies
print_info "Installing dependencies..."
npm install

# Set NODE_OPTIONS for more memory
export NODE_OPTIONS="--max-old-space-size=512"

# Build the frontend
print_info "Building the frontend with fixed configuration..."
npm run build:minimal

# Check if build was successful
if [ -f "$DIST_DIR/index.html" ]; then
    print_success "Frontend build completed successfully."
    
    # Start the application
    print_info "Starting the application..."
    cd "$SCRIPT_DIR" && ./start-built.sh
else
    print_error "Frontend build failed."
    exit 1
fi
