#!/usr/bin/env bash

# Production startup script for Steel Unit Converter
# Similar to start.sh but optimized for production

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to kill existing processes
kill_existing() {
    echo "Checking for existing processes..."

    # Kill backend processes
    BACKEND_PIDS=$(ps aux | grep "uvicorn main:app\|gunicorn main:app" | grep -v grep | awk '{print $2}')
    if [ -n "$BACKEND_PIDS" ]; then
        echo "Killing existing backend processes..."
        for PID in $BACKEND_PIDS; do
            kill -9 $PID 2>/dev/null || true
        done
    fi

    # Kill frontend processes - check for both npm preview and serve
    FRONTEND_PIDS=$(ps aux | grep "npm run preview\|serve -s dist" | grep -v grep | awk '{print $2}')
    if [ -n "$FRONTEND_PIDS" ]; then
        echo "Killing existing frontend processes..."
        for PID in $FRONTEND_PIDS; do
            kill -9 $PID 2>/dev/null || true
        done
    fi

    # Also check for any processes on port 3000
    PORT_3000_PID=$(lsof -t -i:3000 2>/dev/null)
    if [ -n "$PORT_3000_PID" ]; then
        echo "Killing process on port 3000..."
        kill -9 $PORT_3000_PID 2>/dev/null || true
    fi

    # Also check for any processes on port 8000
    PORT_8000_PID=$(lsof -t -i:8000 2>/dev/null)
    if [ -n "$PORT_8000_PID" ]; then
        echo "Killing process on port 8000..."
        kill -9 $PORT_8000_PID 2>/dev/null || true
    fi

    echo "All existing processes killed."
}

# Check if python is installed
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi

    # Check Python version
    PYTHON_VERSION=$(python3 --version | awk '{print $2}')
    echo "Found Python version: $PYTHON_VERSION"

    # Create alias for python if it doesn't exist
    if ! command -v python &> /dev/null; then
        echo "Creating alias for python -> python3"
        alias python=python3
    fi
}

# Check if node is installed
check_node() {
    if ! command -v node &> /dev/null; then
        echo "Node.js is not installed. Please install Node.js 14 or higher."
        exit 1
    fi

    # Check Node.js version
    NODE_VERSION=$(node --version)
    echo "Found Node.js version: $NODE_VERSION"

    if ! command -v npm &> /dev/null; then
        echo "npm is not installed. Please install npm."
        exit 1
    fi
}

# Check and setup nginx if available
setup_nginx() {
    echo "Setting up Nginx for production..."

    # Check if nginx is installed
    if ! command -v nginx &> /dev/null; then
        echo "Nginx is not installed. Using direct frontend server instead."
        echo "Note: Without nginx, you'll need to access the application directly at:"
        echo "  Frontend: http://localhost:3000"
        echo "  Backend: http://localhost:8000"
        echo ""
        return 0
    fi

    # Check if our setup-nginx.sh script exists
    if [ -f "$SCRIPT_DIR/setup-nginx.sh" ]; then
        echo "Running setup-nginx.sh script..."
        # Run the script but don't fail if it doesn't work
        bash "$SCRIPT_DIR/setup-nginx.sh" || {
            echo "Nginx setup failed, but the application will still be accessible directly."
            echo "  Frontend: http://localhost:3000"
            echo "  Backend: http://localhost:8000"
            return 0
        }
    else
        echo "setup-nginx.sh script not found. Using default nginx configuration."

        # Create SSL directory if it doesn't exist
        SSL_DIR="$SCRIPT_DIR/ssl"
        mkdir -p "$SSL_DIR"

        # Generate self-signed certificates if they don't exist
        if [ ! -f "$SSL_DIR/cert.pem" ] || [ ! -f "$SSL_DIR/key.pem" ]; then
            echo "Generating self-signed SSL certificates..."
            openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                -keyout "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" \
                -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost" || true
        fi

        # Create nginx configuration
        echo "Creating nginx configuration..."
        NGINX_CONF_DIR=""
        if [ -d "/etc/nginx/conf.d" ]; then
            NGINX_CONF_DIR="/etc/nginx/conf.d"
        elif [ -d "/usr/local/etc/nginx/conf.d" ]; then
            NGINX_CONF_DIR="/usr/local/etc/nginx/conf.d"
        elif [ -d "/usr/local/etc/nginx/servers" ]; then
            NGINX_CONF_DIR="/usr/local/etc/nginx/servers"
        else
            echo "Could not find nginx configuration directory. Using direct frontend server instead."
            return 0
        fi

        # Create nginx configuration file
        cat > /tmp/steelnet.conf << EOF
server {
    listen 80;
    server_name localhost;

    # Serve favicon.ico directly (prevent duplicate handling)
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
        alias $FRONTEND_DIR/dist/favicon.ico;
    }

    # Redirect all HTTP requests to HTTPS
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name localhost;

    # SSL configuration
    ssl_certificate $SSL_DIR/cert.pem;
    ssl_certificate_key $SSL_DIR/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';

    # SSL optimizations for low memory
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 5m;

    # Root directory for static files
    root $FRONTEND_DIR/dist;
    index index.html;

    # Serve favicon.ico directly (prevent duplicate handling)
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
    }

    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
        expires max;
        add_header Cache-Control "public, no-transform";
    }

    # Frontend application - important for SPA routing
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF

        # Copy configuration file to nginx directory
        if command -v sudo &> /dev/null; then
            sudo cp /tmp/steelnet.conf "$NGINX_CONF_DIR/steelnet.conf"
        else
            cp /tmp/steelnet.conf "$NGINX_CONF_DIR/steelnet.conf"
        fi

        # Test and reload nginx
        echo "Testing nginx configuration..."
        if command -v sudo &> /dev/null; then
            sudo nginx -t
            if [ $? -eq 0 ]; then
                echo "Nginx configuration is valid. Reloading nginx..."
                if command -v systemctl &> /dev/null; then
                    sudo systemctl reload nginx
                else
                    sudo nginx -s reload
                fi
            else
                echo "Nginx configuration is invalid. Using direct frontend server instead."
            fi
        else
            nginx -t
            if [ $? -eq 0 ]; then
                echo "Nginx configuration is valid. Reloading nginx..."
                nginx -s reload
            else
                echo "Nginx configuration is invalid. Using direct frontend server instead."
            fi
        fi
    fi
}

start_backend() {
    echo "Starting backend in production mode..."

    # Change directory to backend
    cd "$BACKEND_DIR" || exit

    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "Creating virtual environment..."
        python3 -m venv venv
    fi

    # Activate virtual environment
    source venv/bin/activate

    # Install dependencies if requirements.txt exists
    if [ -f "requirements.txt" ]; then
        echo "Installing backend dependencies..."

        # Install system dependencies first
        echo "Checking for system package manager..."
        if command -v apt-get &> /dev/null; then
            echo "Using apt-get to install system dependencies..."
            sudo apt-get update
            sudo apt-get install -y python3-dev libpq-dev default-libmysqlclient-dev build-essential python3-pip python3-venv pipx
        elif command -v yum &> /dev/null; then
            echo "Using yum to install system dependencies..."
            sudo yum install -y python3-devel postgresql-devel mysql-devel gcc python3-pip python3-virtualenv
            # Install pipx if not available
            if ! command -v pipx &> /dev/null; then
                echo "Installing pipx..."
                python3 -m pip install --user pipx
                python3 -m pipx ensurepath
            fi
        fi

        # Function to install packages with pip, with fallbacks for externally managed environments
        install_package() {
            local package=$1
            local install_cmd="python3 -m pip install"

            # Try with --break-system-packages first (for externally managed environments)
            if $install_cmd --break-system-packages $package; then
                echo "Successfully installed $package with --break-system-packages"
                return 0
            fi

            # If that fails, try regular pip install
            echo "--break-system-packages failed, trying regular pip install..."
            if $install_cmd $package; then
                echo "Successfully installed $package"
                return 0
            fi

            # If that fails too, try pipx if available
            if command -v pipx &> /dev/null; then
                echo "Trying with pipx..."
                if pipx install $package --include-deps; then
                    echo "Successfully installed $package with pipx"
                    return 0
                fi
            else
                echo "pipx not found, trying to install it..."
                if command -v apt-get &> /dev/null; then
                    sudo apt-get install -y pipx
                elif command -v yum &> /dev/null; then
                    sudo yum install -y python3-pip
                    python3 -m pip install --user pipx
                    python3 -m pipx ensurepath
                else
                    python3 -m pip install --user pipx || python3 -m pip install --break-system-packages pipx
                fi

                if command -v pipx &> /dev/null; then
                    echo "Trying with newly installed pipx..."
                    if pipx install $package --include-deps; then
                        echo "Successfully installed $package with pipx"
                        return 0
                    fi
                fi
            fi

            echo "Failed to install $package with all methods"
            return 1
        }

        # Install from requirements.txt
        echo "Installing Python dependencies from requirements.txt..."
        python3 -m pip install --break-system-packages -r requirements.txt || python3 -m pip install -r requirements.txt

        # Install specific packages
        install_package "gunicorn"
        install_package "uvicorn"
        install_package "python-dotenv==1.0.0"

        # Check database configuration and install appropriate drivers
        if grep -q "RDS_HOSTNAME" ../.env.production; then
            DB_TYPE=$(grep "RDS_HOSTNAME" ../.env.production | grep -o "mysql\|postgresql" || echo "mysql")

            if [ "$DB_TYPE" = "mysql" ] || [ -z "$DB_TYPE" ]; then
                echo "Installing MySQL dependencies..."
                install_package "pymysql"
                install_package "aiomysql"
            elif [ "$DB_TYPE" = "postgresql" ]; then
                echo "Installing PostgreSQL dependencies..."
                install_package "psycopg2-binary"
                install_package "asyncpg"
            fi
        else
            # Default to SQLite
            echo "Using SQLite as database (no RDS configuration found)"
            install_package "aiosqlite"
        fi
    fi

    # Set environment variables for production
    export ENV="production"
    export DEBUG="False"

    # Fix import issues
    echo "Fixing import issues..."
    python3 fix_imports.py

    # Run dependency checker to install missing packages
    echo "Checking for missing dependencies..."
    python3 install_missing_deps.py

    # Calculate optimal number of workers based on available memory
    # For a 2GB system, we want to be conservative
    TOTAL_MEMORY_MB=$(free -m | awk '/^Mem:/{print $2}')
    echo "Total system memory: $TOTAL_MEMORY_MB MB"

    # Calculate workers based on memory (aim for ~150-200MB per worker)
    # Reserve at least 512MB for the system and other processes
    AVAILABLE_MEMORY_MB=$((TOTAL_MEMORY_MB - 512))
    MEMORY_PER_WORKER=200
    CALCULATED_WORKERS=$((AVAILABLE_MEMORY_MB / MEMORY_PER_WORKER))

    # Ensure at least 1 worker, but no more than 2 for a 2GB system
    WORKERS=$(( CALCULATED_WORKERS > 0 ? CALCULATED_WORKERS : 1 ))
    WORKERS=$(( WORKERS < 2 ? WORKERS : 2 ))

    # Use more threads instead of workers to save memory
    THREADS=4

    echo "Starting backend with Gunicorn ($WORKERS workers, $THREADS threads)..."
    nohup gunicorn main:app \
        --worker-class uvicorn.workers.UvicornWorker \
        --workers $WORKERS \
        --threads $THREADS \
        --bind 0.0.0.0:8000 \
        --max-requests 500 \
        --max-requests-jitter 50 \
        --timeout 120 \
        --keep-alive 5 \
        --log-level warning \
        --worker-tmp-dir /tmp \
        --preload \
        > "$LOG_DIR/backend.log" 2>&1 &

    BACKEND_PID=$!
    echo "Backend started with PID: $BACKEND_PID"
    echo "Backend logs available at: $LOG_DIR/backend.log"

    # Save PID to file for later reference
    echo $BACKEND_PID > "$LOG_DIR/backend.pid"
}

start_frontend() {
    echo "Starting frontend in production mode..."

    # Change directory to frontend
    cd "$FRONTEND_DIR" || exit

    # Install dependencies if needed
    if [ -f "package.json" ]; then
        echo "Installing frontend dependencies..."
        # Use production flag to skip dev dependencies
        npm ci --production || npm install --production

        # Install only necessary dev dependencies for build
        npm install --no-save vite @vitejs/plugin-react typescript
    fi

    # Build frontend for production
    echo "Building frontend for production..."

    # Modify package.json to skip TypeScript checking
    if grep -q "tsc -b && vite build" package.json; then
        echo "Modifying package.json to skip TypeScript checking..."
        sed -i 's/"build": "tsc -b && vite build"/"build": "vite build"/g' package.json
    fi

    # Check if fixed vite config exists
    if [ -f "vite.config.fixed.js" ]; then
        echo "Using fixed vite.config.js for production..."
        cp vite.config.fixed.js vite.config.js
    else
        # Create optimized vite config for production with memory optimizations
        echo "Creating memory-optimized vite.config.js for production..."
        cat > vite.config.js << 'EOF'
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react({
    // Configure the @emotion/babel-plugin
    babel: {
      plugins: [
        [
          '@emotion/babel-plugin',
          {
            // These options are required for proper emotion bundling
            sourceMap: false,
            autoLabel: 'dev-only',
            labelFormat: '[local]',
            cssPropOptimization: true,
          },
        ],
      ],
    },
  })],
  build: {
    outDir: 'dist',
    minify: false, // Disable minification to avoid issues
    sourcemap: false,
    target: 'es2015',
    cssCodeSplit: true,
    assetsInlineLimit: 4096,
    emptyOutDir: true,
    rollupOptions: {
      output: {
        // Don't use manualChunks with specific libraries to avoid circular dependencies
        manualChunks: (id) => {
          // Put React and ReactDOM in the vendor chunk
          if (id.includes('node_modules/react/') ||
              id.includes('node_modules/react-dom/')) {
            return 'vendor';
          }

          // Put React Router in its own chunk
          if (id.includes('node_modules/react-router') ||
              id.includes('node_modules/react-router-dom')) {
            return 'router';
          }

          // Put Material UI components in their own chunk
          if (id.includes('node_modules/@mui/material')) {
            return 'material';
          }

          // Put Material UI icons in their own chunk
          if (id.includes('node_modules/@mui/icons-material')) {
            return 'icons';
          }
        }
      }
    }
  },
  // Reduce memory usage during development
  server: {
    hmr: {
      overlay: false
    },
    watch: {
      usePolling: false
    }
  }
});
EOF
    fi

    # Build the frontend for production
    echo "Building frontend for production..."
    export NODE_OPTIONS="--max-old-space-size=4096"
    npm run build || {
        echo "Build failed with max-old-space-size=512, trying with more minimal approach..."
        export NODE_OPTIONS="--max-old-space-size=4096"
        npm run build:minimal || npm run build
    }

    # Start frontend server using vite preview for production build
    echo "Starting frontend server with vite preview (production mode)..."
    # Set NODE_OPTIONS to limit memory usage
    export NODE_OPTIONS="--max-old-space-size=4096"
    # Ensure server listens on all interfaces (0.0.0.0) for public access
    nohup npm run preview -- --host 0.0.0.0 --port 3000 > "$LOG_DIR/frontend.log" 2>&1 &

    # Store the PID
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"
    echo "Frontend server started with PID: $FRONTEND_PID"
    echo "Frontend logs available at: $LOG_DIR/frontend.log"

    # Check if the server started successfully
    sleep 5
    if ! curl -s http://localhost:3000 > /dev/null; then
        echo "Warning: Frontend server may not have started correctly. Trying alternative method..."
        kill $FRONTEND_PID 2>/dev/null || true

        # Try alternative method with a simple HTTP server
        echo "Starting frontend with a simple HTTP server as fallback..."
        cd "$FRONTEND_DIR/dist" || exit

        # Use Python's built-in HTTP server as a fallback
        if command -v python3 &> /dev/null; then
            nohup python3 -m http.server 3000 > "$LOG_DIR/frontend.log" 2>&1 &
        elif command -v python &> /dev/null; then
            nohup python -m SimpleHTTPServer 3000 > "$LOG_DIR/frontend.log" 2>&1 &
        else
            echo "Error: Could not start a fallback HTTP server. Please install Python."
            exit 1
        fi

        # Store the PID
        FRONTEND_PID=$!
        echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"
        echo "Frontend server started with PID: $FRONTEND_PID (fallback method)"
        echo "Frontend logs available at: $LOG_DIR/frontend.log"
    fi
}

# Function to check if services are running
check_services() {
    echo "Checking if services are running..."

    # Check backend
    if [ -f "$LOG_DIR/backend.pid" ]; then
        BACKEND_PID=$(cat "$LOG_DIR/backend.pid")
        if ps -p $BACKEND_PID > /dev/null; then
            echo "Backend is running with PID: $BACKEND_PID"
        else
            echo "Backend is not running."
        fi
    else
        echo "Backend PID file not found."
    fi

    # Check frontend
    if [ -f "$LOG_DIR/frontend.pid" ]; then
        FRONTEND_PID=$(cat "$LOG_DIR/frontend.pid")
        if ps -p $FRONTEND_PID > /dev/null; then
            echo "Frontend is running with PID: $FRONTEND_PID"
        else
            echo "Frontend is not running."
        fi
    else
        echo "Frontend PID file not found."
    fi
}

# Function to show logs
show_logs() {
    local component=$1

    case "$component" in
        backend)
            echo "Showing backend logs (press Ctrl+C to exit)..."
            tail -f "$LOG_DIR/backend.log"
            ;;
        frontend)
            echo "Showing frontend logs (press Ctrl+C to exit)..."
            tail -f "$LOG_DIR/frontend.log"
            ;;
        all)
            echo "Showing all logs (press Ctrl+C to exit)..."
            tail -f "$LOG_DIR/backend.log" "$LOG_DIR/frontend.log"
            ;;
        *)
            echo "Invalid component. Use 'backend', 'frontend', or 'all'."
            ;;
    esac
}

# Process command line arguments
ACTION="start"
COMPONENT="all"
SHOW_LOGS_AFTER=false
SETUP_URL_ACCESS=false
LOW_MEMORY_MODE=false
AUTO_FIX=true  # Enable auto-fix by default

while [[ $# -gt 0 ]]; do
    case $1 in
        --restart)
            ACTION="restart"
            shift
            ;;
        --stop)
            ACTION="stop"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        --logs)
            if [[ $# -gt 1 && ! $2 == --* ]]; then
                COMPONENT=$2
                shift
            else
                COMPONENT="all"
            fi
            ACTION="logs"
            shift
            ;;
        --backend)
            COMPONENT="backend"
            shift
            ;;
        --frontend)
            COMPONENT="frontend"
            shift
            ;;
        --show-logs)
            SHOW_LOGS_AFTER=true
            shift
            ;;
        --url-access)
            SETUP_URL_ACCESS=true
            shift
            ;;
        --low-memory)
            LOW_MEMORY_MODE=true
            echo "Low memory mode enabled (optimized for 2GB RAM systems)"
            # Force specific memory optimizations
            export NODE_OPTIONS="--max-old-space-size=256"
            # Set memory per worker to a lower value
            MEMORY_PER_WORKER=150
            shift
            ;;
        --build-only)
            # Only build the frontend without starting services
            cd "$FRONTEND_DIR" || exit
            echo "Building frontend only..."
            start_frontend
            echo "Frontend built successfully. Exiting without starting services."
            exit 0
            ;;
        fix-nginx)
            ACTION="fix-nginx"
            shift
            ;;
        fix-all)
            ACTION="fix-all"
            shift
            ;;
        --auto-fix)
            # This flag will automatically fix all issues when used with other commands
            AUTO_FIX=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --restart       Stop existing processes and start new ones"
            echo "  --stop          Stop all processes and exit"
            echo "  --status        Check if services are running"
            echo "  --logs [COMP]   Show logs (backend, frontend, or all)"
            echo "  --backend       Only operate on backend"
            echo "  --frontend      Only operate on frontend"
            echo "  --show-logs     Show logs after starting"
            echo "  --url-access    Set up URL access via nginx"
            echo "  --low-memory    Optimize for low memory environments (2GB RAM)"
            echo "  --auto-fix      Automatically fix all issues (can be combined with other options)"
            echo "  fix-nginx       Fix nginx configuration issues"
            echo "  fix-all         Fix all issues (nginx, frontend serving, favicon, etc.)"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information."
            exit 1
            ;;
    esac
done

# Auto-detect low memory systems if not explicitly set
if [ "$LOW_MEMORY_MODE" = false ]; then
    TOTAL_MEMORY_MB=$(free -m | awk '/^Mem:/{print $2}')
    if [ "$TOTAL_MEMORY_MB" -lt 2500 ]; then
        echo "Auto-detected low memory system ($TOTAL_MEMORY_MB MB). Enabling low memory optimizations."
        LOW_MEMORY_MODE=true
        # Set memory optimizations
        export NODE_OPTIONS="--max-old-space-size=256"
        # Set memory per worker to a lower value
        MEMORY_PER_WORKER=150
    fi
fi

# Function to fix frontend serving
fix_frontend_serving() {
    echo "Fixing frontend serving issues..."

    # Check if the fix-frontend-serving.sh script exists
    if [ -f "$SCRIPT_DIR/fix-frontend-serving.sh" ]; then
        echo "Running fix-frontend-serving.sh to properly serve the frontend application..."
        "$SCRIPT_DIR/fix-frontend-serving.sh"
    else
        # Inline implementation if script doesn't exist
        echo "fix-frontend-serving.sh not found. Using inline implementation..."

        # Check if the frontend dist directory exists
        FRONTEND_DIR="$SCRIPT_DIR/frontend"
        DIST_DIR="$FRONTEND_DIR/dist"

        if [ ! -d "$DIST_DIR" ]; then
            echo "Frontend dist directory not found. Building frontend with production config..."
            cd "$FRONTEND_DIR" || exit
            npm run build:prod || npm run build || npx vite build
        fi

        # Create favicon.ico if it doesn't exist
        if [ ! -f "$DIST_DIR/favicon.ico" ]; then
            echo "Creating favicon.ico file..."
            touch "$DIST_DIR/favicon.ico"
        fi

        # Update nginx configuration to serve static files
        if [ -f "/etc/nginx/conf.d/steelnet.conf" ]; then
            echo "Updating nginx configuration to serve static files..."
            cat > /tmp/steelnet.conf << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $PUBLIC_IP steelnet.ai;

    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name $PUBLIC_IP steelnet.ai;

    # SSL configuration
    ssl_certificate $SCRIPT_DIR/ssl/cert.pem;
    ssl_certificate_key $SCRIPT_DIR/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';

    # SSL optimizations
    ssl_session_cache shared:SSL:2m;
    ssl_session_timeout 10m;

    # Root directory for static files
    root $DIST_DIR;
    index index.html;

    # Serve favicon.ico directly
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
    }

    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
        expires max;
        add_header Cache-Control "public, no-transform";
    }

    # Frontend application
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
            sudo cp /tmp/steelnet.conf /etc/nginx/conf.d/steelnet.conf

            # Test and reload nginx
            if sudo nginx -t; then
                echo "Nginx configuration is valid. Reloading nginx..."
                sudo systemctl reload nginx || sudo service nginx reload
            else
                echo "Nginx configuration is invalid. Please check the configuration manually."
            fi
        else
            echo "Nginx configuration file not found. Please run fix-nginx-config.sh first."
        fi
    fi
}

# Function to fix nginx configuration
fix_nginx_config() {
    echo "Fixing nginx configuration issues..."

    # Check if the fix-nginx-config-v2.sh script exists
    if [ -f "$SCRIPT_DIR/fix-nginx-config-v2.sh" ]; then
        echo "Running fix-nginx-config-v2.sh to fix nginx configuration..."
        "$SCRIPT_DIR/fix-nginx-config-v2.sh"
    # Check if the fix-nginx-config.sh script exists
    elif [ -f "$SCRIPT_DIR/fix-nginx-config.sh" ]; then
        echo "Running fix-nginx-config.sh to fix nginx configuration..."
        "$SCRIPT_DIR/fix-nginx-config.sh"
    else
        # Inline implementation if script doesn't exist
        echo "Nginx configuration fix scripts not found. Using inline implementation..."

        # Create SSL directory if it doesn't exist
        if [ ! -d "$SCRIPT_DIR/ssl" ]; then
            echo "Creating SSL directory..."
            mkdir -p "$SCRIPT_DIR/ssl"
        fi

        # Generate self-signed certificates if they don't exist
        if [ ! -f "$SCRIPT_DIR/ssl/cert.pem" ] || [ ! -f "$SCRIPT_DIR/ssl/key.pem" ]; then
            echo "Generating self-signed SSL certificates..."
            openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                -keyout "$SCRIPT_DIR/ssl/key.pem" -out "$SCRIPT_DIR/ssl/cert.pem" \
                -subj "/C=CN/ST=State/L=City/O=Organization/CN=steelnet.ai"
        fi

        # Detect nginx user
        echo "Detecting nginx user..."
        NGINX_USER=""
        # Try to get nginx user from process list
        NGINX_USER=$(ps aux | grep -E "nginx.*worker" | grep -v grep | awk '{print $1}' | head -n 1)

        # If not found, try to get from configuration
        if [ -z "$NGINX_USER" ]; then
            if [ -f "/etc/nginx/nginx.conf" ]; then
                NGINX_USER=$(grep -E "^user" /etc/nginx/nginx.conf | awk '{print $2}' | sed 's/;$//')
            fi
        fi

        # If still not found, check common users
        if [ -z "$NGINX_USER" ]; then
            for user in www-data http nginx nobody; do
                if id -u "$user" >/dev/null 2>&1; then
                    NGINX_USER="$user"
                    break
                fi
            done
        fi

        # If still not found, use the current user
        if [ -z "$NGINX_USER" ]; then
            NGINX_USER=$(whoami)
            echo "Could not detect nginx user, using current user: $NGINX_USER"
        else
            echo "Detected nginx user: $NGINX_USER"
        fi

        # Create optimized nginx.conf
        echo "Creating optimized nginx.conf..."
        cat > /tmp/nginx.conf << EOF
user $NGINX_USER;
worker_processes 1;
worker_rlimit_nofile 1024;
pid /var/run/nginx.pid;

events {
    worker_connections 512;
    multi_accept off;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Buffer size settings (reduced for low memory)
    client_body_buffer_size 8k;
    client_header_buffer_size 1k;
    client_max_body_size 1m;
    large_client_header_buffers 2 1k;

    # Gzip settings (save CPU)
    gzip on;
    gzip_min_length 1000;
    gzip_comp_level 2;
    gzip_types text/plain text/css application/javascript application/json;

    # Logging settings
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF
        sudo cp /tmp/nginx.conf /etc/nginx/nginx.conf

        # Test nginx configuration
        if sudo nginx -t; then
            echo "Nginx main configuration is valid."
        else
            echo "Nginx main configuration is invalid. Please check the configuration manually."
        fi

        # Start or restart nginx
        if systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1; then
            echo "Restarting nginx..."
            sudo systemctl restart nginx || sudo service nginx restart
        else
            echo "Starting nginx..."
            sudo systemctl start nginx || sudo service nginx start
        fi
    fi
}

# Function to fix favicon.ico issues
fix_favicon() {
    echo "Fixing favicon.ico issues..."

    # Check if the fix-favicon.sh script exists
    if [ -f "$SCRIPT_DIR/fix-favicon.sh" ]; then
        echo "Running fix-favicon.sh to fix favicon.ico issues..."
        "$SCRIPT_DIR/fix-favicon.sh"
    else
        # Inline implementation if script doesn't exist
        echo "fix-favicon.sh not found. Using inline implementation..."

        FRONTEND_DIR="$SCRIPT_DIR/frontend"
        DIST_DIR="$FRONTEND_DIR/dist"

        # Create favicon.ico if it doesn't exist
        if [ ! -f "$DIST_DIR/favicon.ico" ]; then
            echo "Creating favicon.ico file..."
            if command -v curl &> /dev/null; then
                curl -s -o "$DIST_DIR/favicon.ico" https://www.google.com/favicon.ico
            else
                touch "$DIST_DIR/favicon.ico"
            fi
        fi
    fi
}

# Function to check and fix all issues
fix_all_issues() {
    echo "Checking and fixing all issues..."

    # Fix nginx configuration only if nginx is installed
    if command -v nginx &> /dev/null; then
        echo "Fixing nginx configuration issues..."
        fix_nginx_config || echo "Nginx configuration fix failed, but the application will still be accessible directly."
    else
        echo "Nginx is not installed. Skipping nginx configuration fixes."
    fi

    # Fix frontend serving
    fix_frontend_serving || echo "Frontend serving fix failed, but the application should still work."

    # Fix favicon.ico issues
    fix_favicon || echo "Favicon fix failed, but this is not critical."

    # Check if ports are open
    echo "Checking if ports are open..."
    if command -v netstat &> /dev/null; then
        echo "Port 8000 (Backend):"
        netstat -tulpn 2>/dev/null | grep ":8000 " || echo "Port 8000 is not open"

        echo "Port 3000 (Frontend):"
        netstat -tulpn 2>/dev/null | grep ":3000 " || echo "Port 3000 is not open"

        # Only check nginx ports if nginx is installed
        if command -v nginx &> /dev/null; then
            echo "Port 80 (HTTP):"
            netstat -tulpn 2>/dev/null | grep ":80 " || echo "Port 80 is not open"

            echo "Port 443 (HTTPS):"
            netstat -tulpn 2>/dev/null | grep ":443 " || echo "Port 443 is not open"
        fi
    elif command -v ss &> /dev/null; then
        echo "Port 8000 (Backend):"
        ss -tulpn 2>/dev/null | grep ":8000 " || echo "Port 8000 is not open"

        echo "Port 3000 (Frontend):"
        ss -tulpn 2>/dev/null | grep ":3000 " || echo "Port 3000 is not open"

        # Only check nginx ports if nginx is installed
        if command -v nginx &> /dev/null; then
            echo "Port 80 (HTTP):"
            ss -tulpn 2>/dev/null | grep ":80 " || echo "Port 80 is not open"

            echo "Port 443 (HTTPS):"
            ss -tulpn 2>/dev/null | grep ":443 " || echo "Port 443 is not open"
        fi
    fi

    # Check firewall
    echo "Checking firewall..."
    if command -v ufw &> /dev/null; then
        ufw status 2>/dev/null || echo "Cannot check ufw status (may need sudo)"

        # Check if ports 3000 and 8000 are allowed
        if ! ufw status 2>/dev/null | grep -E "3000|8000" | grep -q "ALLOW"; then
            echo "Ports 3000 and 8000 may not be allowed in the firewall."
            echo "Consider running: sudo ufw allow 3000/tcp && sudo ufw allow 8000/tcp"
        fi

        # Only check nginx ports if nginx is installed
        if command -v nginx &> /dev/null; then
            # Check if ports 80 and 443 are allowed
            if ! ufw status 2>/dev/null | grep -E "80|443" | grep -q "ALLOW"; then
                echo "Ports 80 and 443 may not be allowed in the firewall."
                echo "Consider running: sudo ufw allow 80/tcp && sudo ufw allow 443/tcp"
            fi
        fi
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --list-all 2>/dev/null || echo "Cannot check firewall-cmd status (may need sudo)"

        # Check if ports 3000 and 8000 are allowed
        if ! firewall-cmd --list-ports 2>/dev/null | grep -E "3000|8000" > /dev/null; then
            echo "Ports 3000 and 8000 may not be allowed in the firewall."
            echo "Consider running: sudo firewall-cmd --permanent --add-port=3000/tcp && sudo firewall-cmd --permanent --add-port=8000/tcp && sudo firewall-cmd --reload"
        fi

        # Only check nginx ports if nginx is installed
        if command -v nginx &> /dev/null; then
            # Check if ports 80 and 443 are allowed
            if ! firewall-cmd --list-ports 2>/dev/null | grep -E "80|443" > /dev/null; then
                echo "Ports 80 and 443 may not be allowed in the firewall."
                echo "Consider running: sudo firewall-cmd --permanent --add-port=80/tcp && sudo firewall-cmd --permanent --add-port=443/tcp && sudo firewall-cmd --reload"
            fi
        fi
    elif command -v iptables &> /dev/null; then
        iptables -L 2>/dev/null | grep -E "3000|8000" || echo "No explicit rules for ports 3000/8000 (may need sudo)"

        # Only check nginx ports if nginx is installed
        if command -v nginx &> /dev/null; then
            iptables -L 2>/dev/null | grep -E "80|443" || echo "No explicit rules for ports 80/443 (may need sudo)"
        fi
    fi
}

# Main execution
case "$ACTION" in
    start)
        # Check requirements
        check_python
        check_node

        # Kill existing processes
        kill_existing

        # Start services based on component
        if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "backend" ]; then
            start_backend
        fi

        if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "frontend" ]; then
            start_frontend
        fi

        # Setup nginx and fix all issues
        if [ "$COMPONENT" = "all" ] || [ "$SETUP_URL_ACCESS" = true ]; then
            setup_nginx

            # Fix all issues automatically
            fix_all_issues
        fi

        echo "Services started successfully."

        # Show logs if requested
        if [ "$SHOW_LOGS_AFTER" = true ]; then
            show_logs "$COMPONENT"
        fi
        ;;
    restart)
        # Kill existing processes
        kill_existing

        # Check requirements
        check_python
        check_node

        # Start services based on component
        if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "backend" ]; then
            start_backend
        fi

        if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "frontend" ]; then
            start_frontend
        fi

        # Setup nginx and fix all issues
        if [ "$COMPONENT" = "all" ] || [ "$SETUP_URL_ACCESS" = true ]; then
            setup_nginx

            # Fix all issues automatically
            fix_all_issues
        fi

        echo "Services restarted successfully."

        # Show logs if requested
        if [ "$SHOW_LOGS_AFTER" = true ]; then
            show_logs "$COMPONENT"
        fi
        ;;
    stop)
        kill_existing
        echo "All services stopped."
        ;;
    status)
        check_services
        ;;
    logs)
        show_logs "$COMPONENT"
        ;;
    fix-nginx)
        # Fix all issues
        fix_all_issues
        echo "All issues fixed."
        ;;
    fix-all)
        # Fix all issues
        fix_all_issues
        echo "All issues fixed."
        ;;
    *)
        # If no action is specified but URL access is requested, just set up nginx
        if [ "$SETUP_URL_ACCESS" = true ]; then
            echo "Setting up URL access via nginx..."
            setup_nginx

            # Fix all issues automatically
            fix_all_issues
        else
            echo "No action specified. Use --help for usage information."
            exit 1
        fi
        ;;
esac

echo ""
echo "=================================================="
echo "  Steel Unit Converter - Production Mode  "
echo "=================================================="
echo ""

# Check if nginx is installed and running
if command -v nginx &> /dev/null && (systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1); then
    # Get local IP address
    LOCAL_IP=""
    if command -v ip &> /dev/null; then
        LOCAL_IP=$(ip addr show | grep -E "inet .* global" | grep -v docker | awk '{print $2}' | cut -d/ -f1 | head -n 1)
    elif command -v ifconfig &> /dev/null; then
        LOCAL_IP=$(ifconfig | grep -E "inet .* broadcast" | awk '{print $2}' | head -n 1)
    fi

    if [ -z "$LOCAL_IP" ]; then
        LOCAL_IP="localhost"
    fi

    # Set public IP address
    PUBLIC_IP="************"

    echo "Application URLs (via Nginx):"
    echo "  Public IP: https://$PUBLIC_IP"
    echo "  Local IP: https://$LOCAL_IP"
    echo "  Public API: https://$PUBLIC_IP/api"
    echo "  Local API: https://$LOCAL_IP/api"
    echo "  (HTTP access will redirect to HTTPS)"

    # Check if domain is configured
    if grep -q "steelnet.ai" /etc/hosts; then
        echo ""
        echo "Domain access:"
        echo "  HTTPS: https://steelnet.ai"
        echo "  API: https://steelnet.ai/api"
    fi

    echo ""
    echo "Direct access URLs (if needed):"
    echo "  Backend: http://localhost:8000"
    echo "  Frontend: http://localhost:3000"
    echo "  API: http://localhost:8000/api"
else
    echo "Application URLs (direct access):"
    echo "  Backend: http://localhost:8000"
    echo "  Frontend: http://localhost:3000"
    echo "  API: http://localhost:8000/api"
    echo ""
    echo "Public IP access (requires nginx setup):"
    echo "  Public IP: http://************"
    echo ""
    echo "To make the application accessible via URL, run:"
    echo "  ./start-prod.sh --url-access"
    echo ""
    echo "Or restart with URL access:"
    echo "  ./start-prod.sh --restart --url-access"
    echo ""
    echo "This will set up nginx as a reverse proxy to make the application accessible via URL."
    echo ""
    echo "For production use, it's recommended to install nginx as a reverse proxy."
    echo "You can run the setup-nginx.sh script to install and configure nginx."
fi

echo ""
echo "Management commands:"
echo "  Start/Restart:    $0 --restart"
echo "  Stop:             $0 --stop"
echo "  Status:           $0 --status"
echo "  View Logs:        $0 --logs [backend|frontend|all]"
echo "  Setup URL Access: $0 --url-access"
echo "  Low Memory Mode:  $0 --low-memory"
echo "  Fix Nginx:        $0 fix-nginx"
echo "  Fix All Issues:   $0 fix-all"
echo ""
if [ "$LOW_MEMORY_MODE" = true ]; then
    echo "Low memory mode is currently ENABLED"
else
    echo "Low memory mode is currently disabled"
fi
echo ""
