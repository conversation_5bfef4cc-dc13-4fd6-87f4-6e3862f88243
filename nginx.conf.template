# Steel Unit Converter nginx Configuration

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name localhost;
    
    # Serve favicon.ico directly (prevent duplicate handling)
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
        alias /path/to/frontend/dist/favicon.ico;
    }
    
    # Redirect all HTTP requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name localhost;
    
    # SSL configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # SSL optimizations for low memory
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 5m;
    
    # Root directory for static files
    root /path/to/frontend/dist;
    index index.html;
    
    # Serve favicon.ico directly (prevent duplicate handling)
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
    }
    
    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
        expires max;
        add_header Cache-Control "public, no-transform";
    }
    
    # Frontend application - important for SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
