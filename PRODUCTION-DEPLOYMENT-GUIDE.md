# Production Deployment Guide for 2GB Memory Systems

This guide explains how to deploy the Steel Unit Converter application on a production server with limited memory (2GB).

## Overview

The deployment process consists of two main steps:

1. **Build on Development Machine**: Build the frontend on a development machine with sufficient memory
2. **Deploy to Production Server**: Transfer the pre-built files to the production server and run the application

This approach avoids memory allocation errors during the build process on the production server.

## Step 1: Build on Development Machine

On your development machine with sufficient memory (4GB+ recommended):

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/steel-unit-converter.git
   cd steel-unit-converter
   ```

2. Run the build script:
   ```bash
   ./build-for-production.sh
   ```

   This script will:
   - Build the frontend with optimized settings
   - Create a deployment package with pre-built files
   - Generate a `steel-unit-converter-deploy.tar.gz` file

3. Transfer the deployment package to your production server:
   ```bash
   scp steel-unit-converter-deploy.tar.gz user@your-production-server:/path/to/deployment/
   ```

## Step 2: Deploy to Production Server

On your production server with 2GB memory:

1. Extract the deployment package:
   ```bash
   cd /path/to/deployment/
   tar -xzf steel-unit-converter-deploy.tar.gz
   ```

2. Update the backend configuration:
   ```bash
   cd /path/to/deployment/
   nano backend/.env.production
   ```
   
   Update the following settings:
   - Database connection details (RDS_HOSTNAME, RDS_USERNAME, RDS_PASSWORD, etc.)
   - Security settings (SECRET_KEY, CORS_ORIGINS)
   - Email settings (if applicable)

3. Start the application:
   ```bash
   ./start-built.sh
   ```

   This script will:
   - Start the backend with memory-optimized settings (1 worker, 4 threads)
   - Serve the pre-built frontend files without rebuilding
   - Apply various memory optimizations throughout the stack

4. (Optional) Set up nginx for URL access:
   ```bash
   ./start-built.sh --url-access
   ```

## Managing the Application

### Starting the Application
```bash
./start-built.sh
```

### Stopping the Application
```bash
./start-built.sh stop
```

### Checking Status
```bash
./start-built.sh status
```

### Viewing Logs
```bash
./start-built.sh logs
```

## Memory Optimizations

The following memory optimizations are applied:

### Backend Optimizations

1. **Single Worker Configuration**:
   - Uses only 1 Gunicorn worker instead of multiple workers
   - Uses 4 threads per worker to handle concurrent requests
   - Reduces memory usage while maintaining concurrency

2. **Gunicorn Settings**:
   - Uses `--max-requests 500` to recycle workers periodically
   - Uses `--max-requests-jitter 50` to prevent all workers from restarting simultaneously
   - Uses `--preload` to share application code between workers

### Frontend Optimizations

1. **Pre-built Frontend**:
   - Uses pre-built frontend files instead of rebuilding on the production server
   - Eliminates memory-intensive build process on the production server

2. **Serving Optimizations**:
   - Uses `serve` for static file serving with minimal memory footprint
   - Sets Node.js memory limit to 256MB

### Nginx Optimizations (Optional)

If you choose to use nginx as a reverse proxy:

1. **Process and Connection Limits**:
   - Sets `worker_processes 1` to use a single worker
   - Limits file descriptors and connections
   - Disables `multi_accept` to reduce CPU usage

2. **Buffer Size Reductions**:
   - Reduces buffer sizes to minimize memory usage
   - Optimizes SSL settings for low memory usage

3. **Static File Handling**:
   - Properly handles favicon.ico to prevent duplicate processing
   - Adds caching for static assets with a 30-day expiration

## Troubleshooting

### Memory Issues

If you encounter memory issues:

1. **Check Memory Usage**:
   ```bash
   free -m
   ```

2. **Monitor Process Memory**:
   ```bash
   ps -o pid,user,%mem,command ax | grep -E 'gunicorn|node|nginx' | sort -b -k3 -r
   ```

3. **Restart Services**:
   ```bash
   ./start-built.sh stop
   ./start-built.sh
   ```

### Frontend Serving Issues

If the frontend fails to serve:

1. Check if the dist directory exists and contains the necessary files:
   ```bash
   ls -la frontend/dist
   ```

2. Check the frontend logs:
   ```bash
   cat logs/frontend.log
   ```

3. Try installing serve globally:
   ```bash
   npm install -g serve
   ```

### Nginx Issues

If nginx fails to start or serve the application:

1. Check nginx error logs:
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

2. Verify nginx configuration:
   ```bash
   sudo nginx -t
   ```

3. Restart nginx:
   ```bash
   sudo systemctl restart nginx
   # or
   sudo nginx -s reload
   ```

## Updating the Application

To update the application:

1. Build a new version on your development machine:
   ```bash
   ./build-for-production.sh
   ```

2. Transfer the new deployment package to your production server

3. Extract the new package and start the application:
   ```bash
   tar -xzf steel-unit-converter-deploy.tar.gz
   ./start-built.sh
   ```

## Production Considerations

For optimal performance in production:

1. **Use a CDN** for static assets to reduce server load
2. **Enable caching** wherever possible
3. **Monitor memory usage** regularly
4. **Schedule periodic restarts** to prevent memory leaks
5. **Consider upgrading memory** if possible for better performance
