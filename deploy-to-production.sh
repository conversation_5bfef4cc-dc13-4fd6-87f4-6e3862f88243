#!/usr/bin/env bash

# Steel Unit Converter - Deploy to Production Script
# This script deploys the pre-built application to a production server

# Default values
DEPLOY_PACKAGE="steel-unit-converter-deploy.tar.gz"
REMOTE_USER=""
REMOTE_HOST=""
REMOTE_PATH=""
SSH_KEY=""
BUILD_FIRST=true

# Function to print colored output
print_info() {
    echo -e "\033[0;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[0;32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[0;33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $1"
}

# Function to print usage
print_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -u, --user USER       Remote server username"
    echo "  -h, --host HOST       Remote server hostname or IP"
    echo "  -p, --path PATH       Remote server deployment path"
    echo "  -k, --key KEY         SSH private key file"
    echo "  -b, --build           Build the application before deploying (default: true)"
    echo "  -n, --no-build        Skip building the application"
    echo "  --help                Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 -u admin -h example.com -p /var/www/steel-converter -k ~/.ssh/id_rsa"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--user)
            REMOTE_USER="$2"
            shift 2
            ;;
        -h|--host)
            REMOTE_HOST="$2"
            shift 2
            ;;
        -p|--path)
            REMOTE_PATH="$2"
            shift 2
            ;;
        -k|--key)
            SSH_KEY="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_FIRST=true
            shift
            ;;
        -n|--no-build)
            BUILD_FIRST=false
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Check required parameters
if [ -z "$REMOTE_USER" ] || [ -z "$REMOTE_HOST" ] || [ -z "$REMOTE_PATH" ]; then
    print_error "Missing required parameters."
    print_usage
    exit 1
fi

# Build the application if requested
if [ "$BUILD_FIRST" = true ]; then
    print_info "Building the application..."
    ./build-for-production.sh
    
    if [ $? -ne 0 ]; then
        print_error "Build failed. Aborting deployment."
        exit 1
    fi
fi

# Check if deployment package exists
if [ ! -f "$DEPLOY_PACKAGE" ]; then
    print_error "Deployment package not found: $DEPLOY_PACKAGE"
    print_error "Please run ./build-for-production.sh first or specify the correct package path."
    exit 1
fi

# Prepare SSH command
SSH_CMD="ssh"
SCP_CMD="scp"
if [ -n "$SSH_KEY" ]; then
    SSH_CMD="$SSH_CMD -i $SSH_KEY"
    SCP_CMD="$SCP_CMD -i $SSH_KEY"
fi

# Create remote directory if it doesn't exist
print_info "Creating remote directory if it doesn't exist..."
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "mkdir -p $REMOTE_PATH"

# Transfer deployment package
print_info "Transferring deployment package to remote server..."
$SCP_CMD $DEPLOY_PACKAGE $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

# Extract deployment package on remote server
print_info "Extracting deployment package on remote server..."
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_PATH && tar -xzf $DEPLOY_PACKAGE && chmod +x start-built.sh"

# Stop any existing services
print_info "Stopping any existing services..."
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_PATH && ./start-built.sh stop"

# Start the application
print_info "Starting the application..."
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_PATH && ./start-built.sh"

# Set up nginx if available
print_info "Setting up nginx if available..."
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_PATH && command -v nginx &> /dev/null && ./start-built.sh --url-access || echo 'nginx not installed, skipping URL access setup.'"

# Check application status
print_info "Checking application status..."
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_PATH && ./start-built.sh status"

print_success "Deployment completed successfully."
print_info "You can now access the application at:"
print_info "  http://$REMOTE_HOST"
print_info "  https://$REMOTE_HOST (if nginx is configured)"
print_info ""
print_info "To manage the application on the remote server:"
print_info "  ssh $REMOTE_USER@$REMOTE_HOST"
print_info "  cd $REMOTE_PATH"
print_info "  ./start-built.sh [start|stop|status|logs]"
