#!/usr/bin/env bash

# Script to add build-only option to start-prod.sh

# Set script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
START_PROD_SH="$SCRIPT_DIR/start-prod.sh"

# Check if start-prod.sh exists
if [ ! -f "$START_PROD_SH" ]; then
    echo "Error: start-prod.sh not found at $START_PROD_SH"
    exit 1
fi

# Add build-only option to start-prod.sh
echo "Adding build-only option to start-prod.sh..."

# Add build-only option to variables section
sed -i '9a\
# Default values\
ACTION="start"\
COMPONENT="all"\
SHOW_LOGS_AFTER=false\
SETUP_URL_ACCESS=false\
LOW_MEMORY_MODE=false\
' "$START_PROD_SH"

# Add build-only option to command line arguments section
# Find the line with "case "$1" in"
LINE_NUM=$(grep -n "case \"\$1\" in" "$START_PROD_SH" | cut -d: -f1)
if [ -n "$LINE_NUM" ]; then
    # Add build-only option to case statement
    sed -i "$LINE_NUM,/esac/s/--help)/--help|--build-only)/" "$START_PROD_SH"
    sed -i "$LINE_NUM,/esac/s/--help|--build-only)/--build-only)\n            ACTION=\"build-only\"\n            shift\n            ;;\n        --help)/" "$START_PROD_SH"
else
    echo "Error: Could not find command line arguments section in start-prod.sh"
    exit 1
fi

# Add build-only case to main execution section
# Find the line with "case "$ACTION" in"
LINE_NUM=$(grep -n "case \"\$ACTION\" in" "$START_PROD_SH" | cut -d: -f1)
if [ -n "$LINE_NUM" ]; then
    # Add build-only case to case statement
    sed -i "$LINE_NUM,/esac/s/fix-all)/fix-all|build-only)/" "$START_PROD_SH"
    sed -i "$LINE_NUM,/esac/s/fix-all|build-only)/build-only)\n        # Check requirements\n        check_python\n        check_node\n\n        # Change directory to frontend\n        cd \"\$FRONTEND_DIR\" || exit\n\n        # Install dependencies if needed\n        if [ -f \"package.json\" ]; then\n            echo \"Installing frontend dependencies...\"\n            # Use production flag to skip dev dependencies\n            npm ci --production || npm install --production\n\n            # Install only necessary dev dependencies for build\n            npm install --no-save vite @vitejs\/plugin-react typescript\n        fi\n\n        # Build frontend for production\n        echo \"Building frontend for production...\"\n\n        # Modify package.json to skip TypeScript checking\n        if grep -q \"tsc -b && vite build\" package.json; then\n            echo \"Modifying package.json to skip TypeScript checking...\"\n            sed -i 's\/\"build\": \"tsc -b && vite build\"\/\"build\": \"vite build\"\/g' package.json\n        fi\n\n        # Check if fixed vite config exists\n        if [ -f \"vite.config.fixed.js\" ]; then\n            echo \"Using fixed vite.config.js for production...\"\n            cp vite.config.fixed.js vite.config.js\n        fi\n\n        # Build the frontend\n        export NODE_OPTIONS=\"--max-old-space-size=4096\"\n        npm run build\n\n        echo \"Frontend build completed successfully.\"\n        ;;\n        fix-all)/" "$START_PROD_SH"
else
    echo "Error: Could not find main execution section in start-prod.sh"
    exit 1
fi

echo "Successfully added build-only option to start-prod.sh"
