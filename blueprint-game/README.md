# 蓝图连接游戏

一个基于节点连接的益智游戏，灵感来源于Unreal Engine的蓝图系统。

## 游戏概述

玩家需要通过拖拽连接不同的节点端口来构建有效的数据流图。游戏的目标是创建一个从起点到终点的有向无环图，确保所有连接都是有效的。

## 游戏元素

### 节点 (Nodes)
- **起点节点**: 绿色，只有输出端口，数据流的起始点
- **终点节点**: 红色，只有输入端口，数据流的终点
- **普通节点**: 蓝色，有输入和输出端口，用于数据处理

### 端口 (Ports)
- **形状**: 方形、圆形、三角形、菱形
- **颜色**: 红、蓝、绿、黄、紫
- **方向**: 
  - 输入端口（左侧）：接收数据
  - 输出端口（右侧）：发送数据

### 连接规则
1. 只能连接相同形状和颜色的端口
2. 输出端口只能连接到输入端口
3. 每个端口只能有一个连接
4. 不能形成循环连接

## 游戏模式

### 俄罗斯方块模式
- 节点会定期自动生成到临时区域
- 临时区域有容量限制（最多6个节点）
- 需要及时将节点移动到放置区域并连接
- 临时区域满了游戏结束

### 回合制模式
- 每回合30秒时间限制
- 每回合开始时生成新节点
- 现有节点可能发生随机变化（添加/删除端口，改变端口类型）
- 无效连接会被自动清除

## 操作方法

### 基本操作
- **移动节点**: 拖拽节点主体
- **连接端口**: 从输出端口拖拽到输入端口
- **添加节点**: 双击临时区域的节点将其移动到放置区域
- **断开连接**: 从已连接的输出端口重新开始拖拽

### 按钮功能
- **生成节点**: 手动生成新节点到临时区域
- **验证连接**: 检查当前连接是否形成有效的有向无环图
- **保存游戏**: 保存当前游戏状态到本地存储
- **加载游戏**: 从本地存储加载之前保存的游戏
- **清空**: 清除所有连接和普通节点

## 游戏特性

### 智能节点生成
- 算法会分析现有端口类型
- 70%概率生成兼容的端口类型
- 30%概率生成新的端口类型
- 确保生成的节点池是可解的

### 实时验证和提示
- 每2秒自动检查连接有效性
- 提供智能建议和错误提示
- 高亮显示兼容的端口

### 视觉效果
- 流动动画显示数据流向
- 端口高亮提示兼容性
- 连接线使用贝塞尔曲线美化
- 节点拖拽时的缩放效果

### 统计信息
- 实时显示节点数量和连接数量
- 完成度百分比和进度条
- 分数和回合计数

## 获胜条件

创建一个满足以下条件的连接图：
1. 所有节点按正确的执行顺序连接
2. 没有循环连接
3. 所有终点节点都可以从起点节点到达
4. 所有连接都是有效的（端口类型匹配）

## 技术实现

- **前端**: 纯HTML5 + CSS3 + JavaScript
- **图形**: SVG用于连接线绘制
- **算法**: 拓扑排序用于环检测
- **存储**: LocalStorage用于游戏保存/加载

## 开始游戏

1. 打开 `index.html` 文件
2. 选择游戏模式（俄罗斯方块或回合制）
3. 生成节点并开始连接
4. 验证连接以获得分数

享受这个充满挑战的蓝图连接游戏吧！
