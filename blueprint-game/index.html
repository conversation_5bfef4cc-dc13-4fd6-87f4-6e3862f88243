<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝图连接游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow: hidden;
        }

        .game-container {
            display: flex;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .temp-area {
            width: 200px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #4a90e2;
        }

        .temp-area h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #4a90e2;
        }

        .placement-area {
            flex: 1;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            position: relative;
            border: 2px solid #50c878;
            overflow: hidden;
        }

        .node {
            position: absolute;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            border-radius: 8px;
            border: 2px solid #3498db;
            padding: 10px;
            cursor: move;
            user-select: none;
            min-width: 80px;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
        }

        .node:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }

        .node.start {
            border-color: #27ae60;
            background: linear-gradient(145deg, #27ae60, #2ecc71);
        }

        .node.end {
            border-color: #e74c3c;
            background: linear-gradient(145deg, #e74c3c, #c0392b);
        }

        .node.dragging {
            z-index: 1000;
            transform: scale(1.1);
        }

        .port {
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .port:hover {
            transform: scale(1.3);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .port.input {
            left: -8px;
        }

        .port.output {
            right: -8px;
        }

        .port.square {
            border-radius: 2px;
        }

        .port.circle {
            border-radius: 50%;
        }

        .port.triangle {
            border-radius: 0;
            transform: rotate(45deg);
        }

        .port.diamond {
            border-radius: 0;
            transform: rotate(45deg);
        }

        .port.red { background-color: #e74c3c; }
        .port.blue { background-color: #3498db; }
        .port.green { background-color: #27ae60; }
        .port.yellow { background-color: #f1c40f; }
        .port.purple { background-color: #9b59b6; }

        .connection {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }

        .connection-line {
            stroke: #3498db;
            stroke-width: 3;
            fill: none;
            filter: drop-shadow(0 0 3px rgba(52, 152, 219, 0.6));
        }

        .temp-connection {
            stroke: #f39c12;
            stroke-width: 2;
            stroke-dasharray: 5,5;
            fill: none;
        }

        .controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            background: linear-gradient(145deg, #3498db, #2980b9);
            border: none;
            border-radius: 5px;
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .btn.danger {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
        }

        .btn.success {
            background: linear-gradient(145deg, #27ae60, #229954);
        }

        .score {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 18px;
            font-weight: bold;
        }

        .mode-selector {
            position: absolute;
            bottom: 10px;
            left: 10px;
            display: flex;
            gap: 10px;
        }

        .validation-result {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            text-align: center;
            display: none;
        }

        .flow-animation {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #f1c40f;
            border-radius: 50%;
            pointer-events: none;
            z-index: 100;
        }

        @keyframes flow {
            0% { opacity: 1; }
            100% { opacity: 0; }
        }

        .flow-animation {
            animation: flow 1s ease-in-out;
        }

        .port.highlighted {
            box-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
            transform: scale(1.4) !important;
        }

        .node.suggested {
            border-color: #f1c40f !important;
            box-shadow: 0 0 20px rgba(241, 196, 15, 0.6) !important;
        }

        .connection-line.highlighted {
            stroke: #f1c40f !important;
            stroke-width: 4 !important;
            filter: drop-shadow(0 0 5px rgba(241, 196, 15, 0.8)) !important;
        }

        .game-stats {
            position: absolute;
            bottom: 60px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }

        .progress-bar {
            position: absolute;
            bottom: 10px;
            right: 10px;
            width: 200px;
            height: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="temp-area">
            <h3>节点池</h3>
            <div id="tempNodes"></div>
        </div>
        
        <div class="placement-area" id="placementArea">
            <svg id="connectionSvg" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;">
            </svg>
        </div>
    </div>

    <div class="controls">
        <button class="btn" onclick="generateNodes()">生成节点 (G)</button>
        <button class="btn success" onclick="validateGraph()">验证连接 (V)</button>
        <button class="btn" onclick="saveGame()">保存游戏</button>
        <button class="btn" onclick="loadGame()">加载游戏</button>
        <button class="btn" onclick="showHelp()">帮助 (?)</button>
        <button class="btn danger" onclick="clearAll()">清空</button>
    </div>

    <div class="score">
        <div>分数: <span id="score">0</span></div>
        <div>回合: <span id="round">1</span></div>
        <div>模式: <span id="currentMode">俄罗斯方块</span></div>
    </div>

    <div class="game-stats">
        <div>节点数: <span id="nodeCount">2</span></div>
        <div>连接数: <span id="connectionCount">0</span></div>
        <div>完成度: <span id="completionRate">0%</span></div>
    </div>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="mode-selector">
        <button class="btn" onclick="setMode('tetris')">俄罗斯方块模式</button>
        <button class="btn" onclick="setMode('rounds')">回合制模式</button>
    </div>

    <div class="validation-result" id="validationResult">
        <h3 id="resultTitle"></h3>
        <p id="resultMessage"></p>
        <button class="btn" onclick="hideValidationResult()">确定</button>
    </div>

    <div class="validation-result" id="helpDialog" style="display: none; max-width: 600px;">
        <h3>游戏帮助</h3>
        <div style="text-align: left; line-height: 1.6;">
            <h4>游戏目标：</h4>
            <p>连接节点创建从起点到终点的有效数据流</p>

            <h4>连接规则：</h4>
            <ul>
                <li>相同形状和颜色的端口才能连接</li>
                <li>输出端口(右侧)连接到输入端口(左侧)</li>
                <li>不能形成循环连接</li>
            </ul>

            <h4>操作方法：</h4>
            <ul>
                <li>拖拽节点移动位置</li>
                <li>从输出端口拖拽到输入端口进行连接</li>
                <li>双击临时区域节点移动到放置区域</li>
            </ul>

            <h4>快捷键：</h4>
            <ul>
                <li>G - 生成节点</li>
                <li>V - 验证连接</li>
                <li>Ctrl+S - 保存游戏</li>
                <li>Ctrl+L - 加载游戏</li>
                <li>1/2 - 切换游戏模式</li>
            </ul>
        </div>
        <button class="btn" onclick="hideHelp()">关闭</button>
    </div>

    <script src="game.js"></script>
</body>
</html>
