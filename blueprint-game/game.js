// 游戏状态
let gameState = {
    mode: 'tetris', // 'tetris' or 'rounds'
    score: 0,
    round: 1,
    nodes: [],
    connections: [],
    tempNodes: [],
    draggedNode: null,
    draggedConnection: null,
    isValidGraph: false
};

// 端口类型定义
const PORT_SHAPES = ['square', 'circle', 'triangle', 'diamond'];
const PORT_COLORS = ['red', 'blue', 'green', 'yellow', 'purple'];

// 节点类
class GameNode {
    constructor(id, type = 'normal', x = 0, y = 0) {
        this.id = id;
        this.type = type; // 'start', 'end', 'normal'
        this.x = x;
        this.y = y;
        this.inputPorts = [];
        this.outputPorts = [];
        this.depth = -1; // 用于拓扑排序
        this.element = null;
        
        this.generatePorts();
        this.createElement();
    }
    
    generatePorts() {
        if (this.type === 'start') {
            // 起点只有输出端口
            const numOutputs = Math.floor(Math.random() * 3) + 1;
            for (let i = 0; i < numOutputs; i++) {
                this.outputPorts.push(this.createPort('output', i));
            }
        } else if (this.type === 'end') {
            // 终点只有输入端口
            const numInputs = Math.floor(Math.random() * 3) + 1;
            for (let i = 0; i < numInputs; i++) {
                this.inputPorts.push(this.createPort('input', i));
            }
        } else {
            // 普通节点有输入和输出端口
            const numInputs = Math.floor(Math.random() * 2) + 1;
            const numOutputs = Math.floor(Math.random() * 2) + 1;
            
            for (let i = 0; i < numInputs; i++) {
                this.inputPorts.push(this.createPort('input', i));
            }
            for (let i = 0; i < numOutputs; i++) {
                this.outputPorts.push(this.createPort('output', i));
            }
        }
    }
    
    createPort(direction, index) {
        return {
            id: `${this.id}_${direction}_${index}`,
            direction: direction,
            shape: PORT_SHAPES[Math.floor(Math.random() * PORT_SHAPES.length)],
            color: PORT_COLORS[Math.floor(Math.random() * PORT_COLORS.length)],
            index: index,
            connected: false,
            connection: null
        };
    }
    
    createElement() {
        this.element = document.createElement('div');
        this.element.className = `node ${this.type}`;
        this.element.style.left = this.x + 'px';
        this.element.style.top = this.y + 'px';
        this.element.textContent = this.type === 'start' ? '起点' : 
                                  this.type === 'end' ? '终点' : `节点${this.id}`;
        
        // 添加端口元素
        this.addPortElements();
        
        // 添加拖拽事件
        this.addDragEvents();
    }
    
    addPortElements() {
        // 添加输入端口
        this.inputPorts.forEach((port, index) => {
            const portElement = document.createElement('div');
            portElement.className = `port input ${port.shape} ${port.color}`;
            portElement.style.top = `${20 + index * 25}px`;
            portElement.dataset.portId = port.id;
            
            // 添加端口连接事件
            this.addPortEvents(portElement, port);
            this.element.appendChild(portElement);
        });
        
        // 添加输出端口
        this.outputPorts.forEach((port, index) => {
            const portElement = document.createElement('div');
            portElement.className = `port output ${port.shape} ${port.color}`;
            portElement.style.top = `${20 + index * 25}px`;
            portElement.dataset.portId = port.id;
            
            // 添加端口连接事件
            this.addPortEvents(portElement, port);
            this.element.appendChild(portElement);
        });
    }
    
    addPortEvents(portElement, port) {
        portElement.addEventListener('mousedown', (e) => {
            e.stopPropagation();
            if (port.direction === 'output') {
                startConnection(port, e);
            }
        });
        
        portElement.addEventListener('mouseup', (e) => {
            e.stopPropagation();
            if (port.direction === 'input' && gameState.draggedConnection) {
                completeConnection(port);
            }
        });
        
        portElement.addEventListener('mouseenter', (e) => {
            if (gameState.draggedConnection && port.direction === 'input') {
                e.target.style.transform = 'scale(1.5)';
                e.target.style.boxShadow = '0 0 15px rgba(255, 255, 255, 1)';
            }
        });
        
        portElement.addEventListener('mouseleave', (e) => {
            e.target.style.transform = '';
            e.target.style.boxShadow = '';
        });
    }
    
    addDragEvents() {
        this.element.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('port')) return;
            
            gameState.draggedNode = this;
            this.element.classList.add('dragging');
            
            const rect = this.element.getBoundingClientRect();
            const offsetX = e.clientX - rect.left;
            const offsetY = e.clientY - rect.top;
            
            const mouseMoveHandler = (e) => {
                const placementArea = document.getElementById('placementArea');
                const areaRect = placementArea.getBoundingClientRect();
                
                this.x = e.clientX - areaRect.left - offsetX;
                this.y = e.clientY - areaRect.top - offsetY;
                
                // 边界检查
                this.x = Math.max(0, Math.min(this.x, areaRect.width - this.element.offsetWidth));
                this.y = Math.max(0, Math.min(this.y, areaRect.height - this.element.offsetHeight));
                
                this.element.style.left = this.x + 'px';
                this.element.style.top = this.y + 'px';
                
                updateConnections();
            };
            
            const mouseUpHandler = () => {
                this.element.classList.remove('dragging');
                gameState.draggedNode = null;
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            };
            
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);
        });
    }
    
    getPortPosition(port) {
        const nodeRect = this.element.getBoundingClientRect();
        const placementRect = document.getElementById('placementArea').getBoundingClientRect();
        
        const portElement = this.element.querySelector(`[data-port-id="${port.id}"]`);
        const portRect = portElement.getBoundingClientRect();
        
        return {
            x: portRect.left + portRect.width / 2 - placementRect.left,
            y: portRect.top + portRect.height / 2 - placementRect.top
        };
    }
}

// 连接类
class Connection {
    constructor(outputPort, inputPort) {
        this.outputPort = outputPort;
        this.inputPort = inputPort;
        this.element = null;
        this.createElement();
    }
    
    createElement() {
        const svg = document.getElementById('connectionSvg');
        this.element = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        this.element.classList.add('connection-line');
        svg.appendChild(this.element);
        this.updatePath();
    }
    
    updatePath() {
        const outputNode = gameState.nodes.find(n => 
            n.outputPorts.some(p => p.id === this.outputPort.id));
        const inputNode = gameState.nodes.find(n => 
            n.inputPorts.some(p => p.id === this.inputPort.id));
        
        if (!outputNode || !inputNode) return;
        
        const startPos = outputNode.getPortPosition(this.outputPort);
        const endPos = inputNode.getPortPosition(this.inputPort);
        
        // 创建贝塞尔曲线路径
        const controlOffset = Math.abs(endPos.x - startPos.x) * 0.5;
        const path = `M ${startPos.x} ${startPos.y} C ${startPos.x + controlOffset} ${startPos.y} ${endPos.x - controlOffset} ${endPos.y} ${endPos.x} ${endPos.y}`;
        
        this.element.setAttribute('d', path);
    }
    
    remove() {
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        // 清除端口连接状态
        this.outputPort.connected = false;
        this.outputPort.connection = null;
        this.inputPort.connected = false;
        this.inputPort.connection = null;
    }
}

// 初始化游戏
function initGame() {
    createInitialNodes();
    updateUI();
}

// 创建初始节点（起点和终点）
function createInitialNodes() {
    const placementArea = document.getElementById('placementArea');
    
    // 创建起点
    const startNode = new GameNode('start', 'start', 50, 100);
    gameState.nodes.push(startNode);
    placementArea.appendChild(startNode.element);
    
    // 创建终点
    const endNode = new GameNode('end', 'end', placementArea.offsetWidth - 150, 100);
    gameState.nodes.push(endNode);
    placementArea.appendChild(endNode.element);
}

// 智能节点生成算法
function generateNodes() {
    if (gameState.mode === 'tetris') {
        generateTetrisNodes();
    } else {
        generateRoundNodes();
    }
}

// 俄罗斯方块模式节点生成
function generateTetrisNodes() {
    // 限制临时区域节点数量
    if (gameState.tempNodes.length >= 5) {
        return;
    }

    const numNodes = Math.floor(Math.random() * 2) + 1; // 1-2个节点
    const existingNodes = gameState.nodes.filter(n => n.type !== 'start' && n.type !== 'end');

    for (let i = 0; i < numNodes; i++) {
        const node = generateCompatibleNode(existingNodes);
        addToTempArea(node);
    }
}

// 回合制模式节点生成
function generateRoundNodes() {
    const numNodes = Math.floor(Math.random() * 3) + 2; // 2-4个节点
    const existingNodes = gameState.nodes.filter(n => n.type !== 'start' && n.type !== 'end');

    for (let i = 0; i < numNodes; i++) {
        const node = generateCompatibleNode(existingNodes);
        addToTempArea(node);
    }
}

// 生成兼容的节点
function generateCompatibleNode(existingNodes) {
    const node = new GameNode(`temp_${Date.now()}_${Math.random()}`, 'normal');

    // 分析现有节点的端口类型
    const availablePortTypes = analyzeExistingPorts();

    // 重新生成端口以确保兼容性
    node.inputPorts = [];
    node.outputPorts = [];

    // 生成输入端口（与现有输出端口兼容）
    const numInputs = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < numInputs; i++) {
        if (availablePortTypes.outputs.length > 0 && Math.random() < 0.7) {
            // 70% 概率生成兼容的端口
            const compatiblePort = availablePortTypes.outputs[Math.floor(Math.random() * availablePortTypes.outputs.length)];
            node.inputPorts.push({
                id: `${node.id}_input_${i}`,
                direction: 'input',
                shape: compatiblePort.shape,
                color: compatiblePort.color,
                index: i,
                connected: false,
                connection: null
            });
        } else {
            // 30% 概率生成新类型端口
            node.inputPorts.push(node.createPort('input', i));
        }
    }

    // 生成输出端口
    const numOutputs = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < numOutputs; i++) {
        if (availablePortTypes.inputs.length > 0 && Math.random() < 0.7) {
            // 70% 概率生成兼容的端口
            const compatiblePort = availablePortTypes.inputs[Math.floor(Math.random() * availablePortTypes.inputs.length)];
            node.outputPorts.push({
                id: `${node.id}_output_${i}`,
                direction: 'output',
                shape: compatiblePort.shape,
                color: compatiblePort.color,
                index: i,
                connected: false,
                connection: null
            });
        } else {
            // 30% 概率生成新类型端口
            node.outputPorts.push(node.createPort('output', i));
        }
    }

    // 重新创建元素以反映新的端口
    node.createElement();

    return node;
}

// 分析现有端口类型
function analyzeExistingPorts() {
    const outputs = [];
    const inputs = [];

    gameState.nodes.forEach(node => {
        node.outputPorts.forEach(port => {
            if (!port.connected) {
                outputs.push({ shape: port.shape, color: port.color });
            }
        });

        node.inputPorts.forEach(port => {
            if (!port.connected) {
                inputs.push({ shape: port.shape, color: port.color });
            }
        });
    });

    return { outputs, inputs };
}

// 添加节点到临时区域
function addToTempArea(node) {
    gameState.tempNodes.push(node);

    const tempArea = document.getElementById('tempNodes');
    node.element.style.position = 'relative';
    node.element.style.margin = '10px 0';
    node.element.style.left = '0';
    node.element.style.top = '0';

    // 添加双击移动到放置区域的功能
    node.element.addEventListener('dblclick', () => {
        moveToPlacementArea(node);
    });

    tempArea.appendChild(node.element);
}

// 移动节点到放置区域
function moveToPlacementArea(node) {
    const index = gameState.tempNodes.indexOf(node);
    if (index > -1) {
        gameState.tempNodes.splice(index, 1);
        
        // 重新设置节点位置和样式
        node.x = Math.random() * 300 + 200;
        node.y = Math.random() * 200 + 100;
        node.element.style.position = 'absolute';
        node.element.style.left = node.x + 'px';
        node.element.style.top = node.y + 'px';
        node.element.style.margin = '0';
        
        gameState.nodes.push(node);
        document.getElementById('placementArea').appendChild(node.element);
        
        // 重新添加拖拽事件
        node.addDragEvents();
    }
}

// 开始连接
function startConnection(outputPort, event) {
    if (outputPort.connected) {
        // 如果已连接，先断开
        const existingConnection = gameState.connections.find(c => c.outputPort.id === outputPort.id);
        if (existingConnection) {
            removeConnection(existingConnection);
        }
    }

    gameState.draggedConnection = {
        outputPort: outputPort,
        startPos: null
    };

    // 高亮兼容端口
    highlightCompatiblePorts(outputPort);

    // 创建临时连接线
    const svg = document.getElementById('connectionSvg');
    const tempLine = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    tempLine.classList.add('temp-connection');
    svg.appendChild(tempLine);
    gameState.draggedConnection.tempElement = tempLine;

    const mouseMoveHandler = (e) => {
        updateTempConnection(e);
    };

    const mouseUpHandler = () => {
        if (gameState.draggedConnection.tempElement) {
            gameState.draggedConnection.tempElement.remove();
        }
        clearPortHighlights();
        gameState.draggedConnection = null;
        document.removeEventListener('mousemove', mouseMoveHandler);
        document.removeEventListener('mouseup', mouseUpHandler);
    };

    document.addEventListener('mousemove', mouseMoveHandler);
    document.addEventListener('mouseup', mouseUpHandler);
}

// 更新临时连接线
function updateTempConnection(event) {
    if (!gameState.draggedConnection) return;
    
    const outputNode = gameState.nodes.find(n => 
        n.outputPorts.some(p => p.id === gameState.draggedConnection.outputPort.id));
    
    if (!outputNode) return;
    
    const startPos = outputNode.getPortPosition(gameState.draggedConnection.outputPort);
    const placementRect = document.getElementById('placementArea').getBoundingClientRect();
    const endPos = {
        x: event.clientX - placementRect.left,
        y: event.clientY - placementRect.top
    };
    
    const controlOffset = Math.abs(endPos.x - startPos.x) * 0.5;
    const path = `M ${startPos.x} ${startPos.y} C ${startPos.x + controlOffset} ${startPos.y} ${endPos.x - controlOffset} ${endPos.y} ${endPos.x} ${endPos.y}`;
    
    gameState.draggedConnection.tempElement.setAttribute('d', path);
}

// 完成连接
function completeConnection(inputPort) {
    if (!gameState.draggedConnection) return;
    
    const outputPort = gameState.draggedConnection.outputPort;
    
    // 检查端口兼容性
    if (!arePortsCompatible(outputPort, inputPort)) {
        return;
    }
    
    // 如果输入端口已连接，先断开
    if (inputPort.connected) {
        const existingConnection = gameState.connections.find(c => c.inputPort.id === inputPort.id);
        if (existingConnection) {
            removeConnection(existingConnection);
        }
    }
    
    // 创建新连接
    const connection = new Connection(outputPort, inputPort);
    gameState.connections.push(connection);
    
    // 更新端口状态
    outputPort.connected = true;
    outputPort.connection = connection;
    inputPort.connected = true;
    inputPort.connection = connection;
    
    // 清理临时连接
    if (gameState.draggedConnection.tempElement) {
        gameState.draggedConnection.tempElement.remove();
    }
    gameState.draggedConnection = null;
}

// 检查端口兼容性
function arePortsCompatible(outputPort, inputPort) {
    return outputPort.shape === inputPort.shape && outputPort.color === inputPort.color;
}

// 移除连接
function removeConnection(connection) {
    const index = gameState.connections.indexOf(connection);
    if (index > -1) {
        gameState.connections.splice(index, 1);
        connection.remove();
    }
}

// 更新所有连接
function updateConnections() {
    gameState.connections.forEach(connection => {
        connection.updatePath();
    });
}

// 验证图的有效性
function validateGraph() {
    const result = isValidDAG();
    showValidationResult(result);
    
    if (result.isValid) {
        gameState.score += 100;
        playFlowAnimation();
    }
    
    updateUI();
}

// 检查是否为有效的有向无环图
function isValidDAG() {
    // 重置所有节点的深度
    gameState.nodes.forEach(node => node.depth = -1);
    
    // 找到所有起点
    const startNodes = gameState.nodes.filter(node => node.type === 'start');
    const endNodes = gameState.nodes.filter(node => node.type === 'end');
    
    if (startNodes.length === 0 || endNodes.length === 0) {
        return { isValid: false, message: '缺少起点或终点节点' };
    }
    
    // 构建邻接表
    const adjacencyList = new Map();
    const inDegree = new Map();
    
    gameState.nodes.forEach(node => {
        adjacencyList.set(node.id, []);
        inDegree.set(node.id, 0);
    });
    
    // 添加边
    gameState.connections.forEach(connection => {
        const outputNode = gameState.nodes.find(n => 
            n.outputPorts.some(p => p.id === connection.outputPort.id));
        const inputNode = gameState.nodes.find(n => 
            n.inputPorts.some(p => p.id === connection.inputPort.id));
        
        if (outputNode && inputNode) {
            adjacencyList.get(outputNode.id).push(inputNode.id);
            inDegree.set(inputNode.id, inDegree.get(inputNode.id) + 1);
        }
    });
    
    // 拓扑排序检查环
    const queue = [];
    const visited = new Set();
    
    // 找到所有入度为0的节点
    inDegree.forEach((degree, nodeId) => {
        if (degree === 0) {
            queue.push(nodeId);
        }
    });
    
    let processedCount = 0;
    let depth = 0;
    
    while (queue.length > 0) {
        const currentLevelSize = queue.length;
        
        for (let i = 0; i < currentLevelSize; i++) {
            const nodeId = queue.shift();
            visited.add(nodeId);
            processedCount++;
            
            // 设置节点深度
            const node = gameState.nodes.find(n => n.id === nodeId);
            if (node) node.depth = depth;
            
            // 处理邻接节点
            adjacencyList.get(nodeId).forEach(neighborId => {
                inDegree.set(neighborId, inDegree.get(neighborId) - 1);
                if (inDegree.get(neighborId) === 0) {
                    queue.push(neighborId);
                }
            });
        }
        
        depth++;
    }
    
    // 检查是否有环
    if (processedCount !== gameState.nodes.length) {
        return { isValid: false, message: '图中存在环路' };
    }
    
    // 检查是否所有终点都可达
    const reachableFromStart = new Set();
    const dfs = (nodeId) => {
        if (reachableFromStart.has(nodeId)) return;
        reachableFromStart.add(nodeId);
        adjacencyList.get(nodeId).forEach(neighborId => dfs(neighborId));
    };
    
    startNodes.forEach(node => dfs(node.id));
    
    const unreachableEndNodes = endNodes.filter(node => !reachableFromStart.has(node.id));
    if (unreachableEndNodes.length > 0) {
        return { isValid: false, message: '存在无法到达的终点节点' };
    }
    
    return { isValid: true, message: '图连接正确！' };
}

// 播放流动动画
function playFlowAnimation() {
    const startNodes = gameState.nodes.filter(node => node.type === 'start');
    
    startNodes.forEach(startNode => {
        animateFlow(startNode, 0);
    });
}

// 动画流动效果
function animateFlow(node, delay) {
    setTimeout(() => {
        // 在节点上创建流动粒子
        const particle = document.createElement('div');
        particle.className = 'flow-animation';
        
        const nodePos = {
            x: node.x + node.element.offsetWidth / 2,
            y: node.y + node.element.offsetHeight / 2
        };
        
        particle.style.left = nodePos.x + 'px';
        particle.style.top = nodePos.y + 'px';
        
        document.getElementById('placementArea').appendChild(particle);
        
        // 移除粒子
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 1000);
        
        // 继续到下一个节点
        const connectedNodes = gameState.connections
            .filter(conn => node.outputPorts.some(p => p.id === conn.outputPort.id))
            .map(conn => gameState.nodes.find(n => n.inputPorts.some(p => p.id === conn.inputPort.id)))
            .filter(n => n);
        
        connectedNodes.forEach(nextNode => {
            animateFlow(nextNode, 500);
        });
    }, delay);
}

// 显示验证结果
function showValidationResult(result) {
    const resultDiv = document.getElementById('validationResult');
    const titleElement = document.getElementById('resultTitle');
    const messageElement = document.getElementById('resultMessage');
    
    titleElement.textContent = result.isValid ? '验证成功！' : '验证失败';
    messageElement.textContent = result.message;
    resultDiv.style.display = 'block';
    
    if (result.isValid) {
        resultDiv.style.background = 'rgba(39, 174, 96, 0.9)';
    } else {
        resultDiv.style.background = 'rgba(231, 76, 60, 0.9)';
    }
}

// 隐藏验证结果
function hideValidationResult() {
    document.getElementById('validationResult').style.display = 'none';
}

// 显示帮助
function showHelp() {
    document.getElementById('helpDialog').style.display = 'block';
}

// 隐藏帮助
function hideHelp() {
    document.getElementById('helpDialog').style.display = 'none';
}

// 清空所有
function clearAll() {
    // 清除所有连接
    gameState.connections.forEach(connection => connection.remove());
    gameState.connections = [];
    
    // 清除除起点和终点外的所有节点
    const nodesToRemove = gameState.nodes.filter(node => node.type === 'normal');
    nodesToRemove.forEach(node => {
        const index = gameState.nodes.indexOf(node);
        if (index > -1) {
            gameState.nodes.splice(index, 1);
            if (node.element && node.element.parentNode) {
                node.element.parentNode.removeChild(node.element);
            }
        }
    });
    
    // 清除临时节点
    gameState.tempNodes.forEach(node => {
        if (node.element && node.element.parentNode) {
            node.element.parentNode.removeChild(node.element);
        }
    });
    gameState.tempNodes = [];
}

// 设置游戏模式
function setMode(mode) {
    gameState.mode = mode;
    updateUI();
}

// 更新UI
function updateUI() {
    document.getElementById('score').textContent = gameState.score;
    document.getElementById('round').textContent = gameState.round;
    document.getElementById('currentMode').textContent = gameState.mode === 'tetris' ? '俄罗斯方块' : '回合制';
    document.getElementById('nodeCount').textContent = gameState.nodes.length;
    document.getElementById('connectionCount').textContent = gameState.connections.length;

    // 计算完成度
    const totalPorts = gameState.nodes.reduce((sum, node) =>
        sum + node.inputPorts.length + node.outputPorts.length, 0);
    const connectedPorts = gameState.connections.length * 2;
    const completionRate = totalPorts > 0 ? Math.round((connectedPorts / totalPorts) * 100) : 0;

    document.getElementById('completionRate').textContent = completionRate + '%';
    document.getElementById('progressFill').style.width = completionRate + '%';
}

// 保存游戏
function saveGame() {
    const saveData = {
        gameState: {
            mode: gameState.mode,
            score: gameState.score,
            round: gameState.round,
            nodes: gameState.nodes.map(node => ({
                id: node.id,
                type: node.type,
                x: node.x,
                y: node.y,
                inputPorts: node.inputPorts,
                outputPorts: node.outputPorts
            })),
            connections: gameState.connections.map(conn => ({
                outputPortId: conn.outputPort.id,
                inputPortId: conn.inputPort.id
            })),
            tempNodes: gameState.tempNodes.map(node => ({
                id: node.id,
                type: node.type,
                inputPorts: node.inputPorts,
                outputPorts: node.outputPorts
            }))
        },
        timestamp: new Date().toISOString()
    };

    localStorage.setItem('blueprintGameSave', JSON.stringify(saveData));

    showValidationResult({
        isValid: true,
        message: '游戏已保存！'
    });
}

// 加载游戏
function loadGame() {
    const saveData = localStorage.getItem('blueprintGameSave');
    if (!saveData) {
        showValidationResult({
            isValid: false,
            message: '没有找到保存的游戏！'
        });
        return;
    }

    try {
        const data = JSON.parse(saveData);
        const savedState = data.gameState;

        // 清空当前游戏
        clearAll();

        // 恢复游戏状态
        gameState.mode = savedState.mode;
        gameState.score = savedState.score;
        gameState.round = savedState.round;
        gameState.nodes = [];
        gameState.connections = [];
        gameState.tempNodes = [];

        // 恢复节点
        const placementArea = document.getElementById('placementArea');
        savedState.nodes.forEach(nodeData => {
            const node = new GameNode(nodeData.id, nodeData.type, nodeData.x, nodeData.y);
            node.inputPorts = nodeData.inputPorts;
            node.outputPorts = nodeData.outputPorts;
            node.createElement();

            gameState.nodes.push(node);
            placementArea.appendChild(node.element);
        });

        // 恢复临时节点
        const tempArea = document.getElementById('tempNodes');
        savedState.tempNodes.forEach(nodeData => {
            const node = new GameNode(nodeData.id, nodeData.type);
            node.inputPorts = nodeData.inputPorts;
            node.outputPorts = nodeData.outputPorts;
            node.createElement();

            node.element.style.position = 'relative';
            node.element.style.margin = '10px 0';
            node.element.style.left = '0';
            node.element.style.top = '0';

            node.element.addEventListener('dblclick', () => {
                moveToPlacementArea(node);
            });

            gameState.tempNodes.push(node);
            tempArea.appendChild(node.element);
        });

        // 恢复连接
        savedState.connections.forEach(connData => {
            const outputPort = findPortById(connData.outputPortId);
            const inputPort = findPortById(connData.inputPortId);

            if (outputPort && inputPort) {
                const connection = new Connection(outputPort, inputPort);
                gameState.connections.push(connection);

                outputPort.connected = true;
                outputPort.connection = connection;
                inputPort.connected = true;
                inputPort.connection = connection;
            }
        });

        // 重新启动游戏模式
        setMode(gameState.mode);
        updateUI();

        showValidationResult({
            isValid: true,
            message: '游戏已加载！'
        });

    } catch (error) {
        showValidationResult({
            isValid: false,
            message: '加载游戏失败：数据损坏'
        });
    }
}

// 根据ID查找端口
function findPortById(portId) {
    for (const node of gameState.nodes) {
        const port = [...node.inputPorts, ...node.outputPorts].find(p => p.id === portId);
        if (port) return port;
    }
    return null;
}

// 高亮兼容端口
function highlightCompatiblePorts(sourcePort) {
    gameState.nodes.forEach(node => {
        const targetPorts = sourcePort.direction === 'output' ? node.inputPorts : node.outputPorts;

        targetPorts.forEach(port => {
            if (!port.connected && arePortsCompatible(sourcePort, port)) {
                const portElement = node.element.querySelector(`[data-port-id="${port.id}"]`);
                if (portElement) {
                    portElement.classList.add('highlighted');
                }
            }
        });
    });
}

// 清除端口高亮
function clearPortHighlights() {
    document.querySelectorAll('.port.highlighted').forEach(element => {
        element.classList.remove('highlighted');
    });
}

// 游戏模式管理
let gameTimer = null;
let roundTimer = null;

// 开始俄罗斯方块模式
function startTetrisMode() {
    if (gameTimer) clearInterval(gameTimer);

    gameTimer = setInterval(() => {
        generateTetrisNodes();

        // 检查临时区域是否满了
        if (gameState.tempNodes.length >= 6) {
            gameOver('临时区域已满！');
        }
    }, 5000); // 每5秒生成新节点
}

// 开始回合制模式
function startRoundMode() {
    if (roundTimer) clearTimeout(roundTimer);

    // 每回合30秒
    roundTimer = setTimeout(() => {
        nextRound();
    }, 30000);
}

// 下一回合
function nextRound() {
    gameState.round++;

    // 随机变化现有蓝图
    if (gameState.round > 1) {
        mutateExistingNodes();
    }

    // 生成新节点
    generateRoundNodes();

    updateUI();
    startRoundMode();
}

// 变化现有节点
function mutateExistingNodes() {
    const normalNodes = gameState.nodes.filter(n => n.type === 'normal');

    normalNodes.forEach(node => {
        if (Math.random() < 0.3) { // 30% 概率变化
            const mutationType = Math.floor(Math.random() * 3);

            switch (mutationType) {
                case 0: // 添加端口
                    addRandomPort(node);
                    break;
                case 1: // 移除端口
                    removeRandomPort(node);
                    break;
                case 2: // 改变端口类型
                    changeRandomPortType(node);
                    break;
            }

            // 重新创建节点元素
            const oldElement = node.element;
            node.createElement();
            oldElement.parentNode.replaceChild(node.element, oldElement);
        }
    });

    // 清理无效连接
    cleanupInvalidConnections();
}

// 添加随机端口
function addRandomPort(node) {
    const isInput = Math.random() < 0.5;
    const portArray = isInput ? node.inputPorts : node.outputPorts;
    const direction = isInput ? 'input' : 'output';

    if (portArray.length < 3) {
        const newPort = node.createPort(direction, portArray.length);
        portArray.push(newPort);
    }
}

// 移除随机端口
function removeRandomPort(node) {
    const isInput = Math.random() < 0.5;
    const portArray = isInput ? node.inputPorts : node.outputPorts;

    if (portArray.length > 1) {
        const portToRemove = portArray[Math.floor(Math.random() * portArray.length)];

        // 如果端口有连接，先移除连接
        if (portToRemove.connected) {
            const connection = gameState.connections.find(c =>
                c.inputPort.id === portToRemove.id || c.outputPort.id === portToRemove.id);
            if (connection) {
                removeConnection(connection);
            }
        }

        const index = portArray.indexOf(portToRemove);
        portArray.splice(index, 1);

        // 重新索引端口
        portArray.forEach((port, i) => {
            port.index = i;
            port.id = `${node.id}_${port.direction}_${i}`;
        });
    }
}

// 改变随机端口类型
function changeRandomPortType(node) {
    const allPorts = [...node.inputPorts, ...node.outputPorts];
    const unconnectedPorts = allPorts.filter(p => !p.connected);

    if (unconnectedPorts.length > 0) {
        const port = unconnectedPorts[Math.floor(Math.random() * unconnectedPorts.length)];
        port.shape = PORT_SHAPES[Math.floor(Math.random() * PORT_SHAPES.length)];
        port.color = PORT_COLORS[Math.floor(Math.random() * PORT_COLORS.length)];
    }
}

// 清理无效连接
function cleanupInvalidConnections() {
    const invalidConnections = gameState.connections.filter(connection => {
        return !arePortsCompatible(connection.outputPort, connection.inputPort);
    });

    invalidConnections.forEach(connection => {
        removeConnection(connection);
    });
}

// 游戏结束
function gameOver(reason) {
    if (gameTimer) {
        clearInterval(gameTimer);
        gameTimer = null;
    }

    if (roundTimer) {
        clearTimeout(roundTimer);
        roundTimer = null;
    }

    showValidationResult({
        isValid: false,
        message: `游戏结束：${reason}\n最终分数：${gameState.score}`
    });
}

// 自动验证和提示系统
function autoValidateAndHint() {
    const result = isValidDAG();

    if (result.isValid) {
        // 显示成功提示
        const hintElement = createHintElement('✓ 当前连接有效！', 'success');
        showHint(hintElement);
    } else {
        // 显示错误提示和建议
        const suggestions = generateSuggestions();
        const hintElement = createHintElement(`❌ ${result.message}\n💡 ${suggestions}`, 'error');
        showHint(hintElement);
    }
}

// 生成建议
function generateSuggestions() {
    const unconnectedOutputs = [];
    const unconnectedInputs = [];

    gameState.nodes.forEach(node => {
        node.outputPorts.forEach(port => {
            if (!port.connected) unconnectedOutputs.push(port);
        });
        node.inputPorts.forEach(port => {
            if (!port.connected) unconnectedInputs.push(port);
        });
    });

    if (unconnectedOutputs.length > 0 && unconnectedInputs.length > 0) {
        const compatiblePairs = [];
        unconnectedOutputs.forEach(output => {
            unconnectedInputs.forEach(input => {
                if (arePortsCompatible(output, input)) {
                    compatiblePairs.push({ output, input });
                }
            });
        });

        if (compatiblePairs.length > 0) {
            return '尝试连接兼容的端口（相同形状和颜色）';
        } else {
            return '需要添加更多兼容的节点';
        }
    }

    return '检查所有节点是否正确连接';
}

// 创建提示元素
function createHintElement(message, type) {
    const hint = document.createElement('div');
    hint.style.cssText = `
        position: absolute;
        top: 60px;
        right: 10px;
        padding: 10px 15px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        max-width: 300px;
        white-space: pre-line;
        ${type === 'success' ? 'background: rgba(39, 174, 96, 0.9);' : 'background: rgba(231, 76, 60, 0.9);'}
    `;
    hint.textContent = message;
    return hint;
}

// 显示提示
function showHint(hintElement) {
    // 移除之前的提示
    const existingHint = document.querySelector('.game-hint');
    if (existingHint) {
        existingHint.remove();
    }

    hintElement.className = 'game-hint';
    document.body.appendChild(hintElement);

    // 3秒后自动移除
    setTimeout(() => {
        if (hintElement.parentNode) {
            hintElement.parentNode.removeChild(hintElement);
        }
    }, 3000);
}

// 设置游戏模式（更新版本）
function setMode(mode) {
    // 停止当前模式的计时器
    if (gameTimer) {
        clearInterval(gameTimer);
        gameTimer = null;
    }
    if (roundTimer) {
        clearTimeout(roundTimer);
        roundTimer = null;
    }

    gameState.mode = mode;

    // 启动新模式
    if (mode === 'tetris') {
        startTetrisMode();
    } else if (mode === 'rounds') {
        startRoundMode();
    }

    updateUI();
}

// 添加实时验证
function enableRealTimeValidation() {
    setInterval(() => {
        if (gameState.connections.length > 0) {
            autoValidateAndHint();
        }
    }, 2000); // 每2秒检查一次
}

// 键盘快捷键
document.addEventListener('keydown', (e) => {
    switch(e.key) {
        case 'g':
        case 'G':
            generateNodes();
            break;
        case 'v':
        case 'V':
            validateGraph();
            break;
        case 'c':
        case 'C':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                clearAll();
            }
            break;
        case 's':
        case 'S':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                saveGame();
            }
            break;
        case 'l':
        case 'L':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                loadGame();
            }
            break;
        case '1':
            setMode('tetris');
            break;
        case '2':
            setMode('rounds');
            break;
        case '?':
        case '/':
            if (e.shiftKey) { // Shift + / = ?
                showHelp();
            }
            break;
    }
});

// 错误处理
window.addEventListener('error', (e) => {
    console.error('游戏错误:', e.error);
    showValidationResult({
        isValid: false,
        message: '游戏发生错误，请刷新页面重试'
    });
});

// 防止页面意外关闭时丢失游戏进度
window.addEventListener('beforeunload', (e) => {
    if (gameState.score > 0 || gameState.connections.length > 0) {
        // 自动保存
        try {
            saveGame();
        } catch (error) {
            console.warn('自动保存失败:', error);
        }

        // 提示用户
        e.preventDefault();
        e.returnValue = '您确定要离开吗？游戏进度将会丢失。';
        return e.returnValue;
    }
});

// 添加触摸设备支持
function addTouchSupport() {
    // 为移动设备添加触摸事件支持
    let touchStartPos = null;

    document.addEventListener('touchstart', (e) => {
        touchStartPos = {
            x: e.touches[0].clientX,
            y: e.touches[0].clientY
        };
    });

    document.addEventListener('touchmove', (e) => {
        e.preventDefault(); // 防止页面滚动
    }, { passive: false });

    document.addEventListener('touchend', (e) => {
        if (touchStartPos) {
            const touchEndPos = {
                x: e.changedTouches[0].clientX,
                y: e.changedTouches[0].clientY
            };

            // 如果是轻触（移动距离很小），模拟点击
            const distance = Math.sqrt(
                Math.pow(touchEndPos.x - touchStartPos.x, 2) +
                Math.pow(touchEndPos.y - touchStartPos.y, 2)
            );

            if (distance < 10) {
                const element = document.elementFromPoint(touchEndPos.x, touchEndPos.y);
                if (element) {
                    element.click();
                }
            }
        }
        touchStartPos = null;
    });
}

// 性能优化：节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 优化连接更新性能
const throttledUpdateConnections = throttle(updateConnections, 16); // 60fps

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    try {
        initGame();
        enableRealTimeValidation();
        addTouchSupport();

        // 默认开始俄罗斯方块模式
        setMode('tetris');

        // 显示快捷键提示
        setTimeout(() => {
            showValidationResult({
                isValid: true,
                message: '快捷键提示：\nG - 生成节点\nV - 验证连接\nCtrl+S - 保存\nCtrl+L - 加载\n1/2 - 切换模式'
            });
        }, 1000);

    } catch (error) {
        console.error('游戏初始化失败:', error);
        document.body.innerHTML = '<div style="text-align: center; margin-top: 50px; color: white;"><h1>游戏加载失败</h1><p>请刷新页面重试</p></div>';
    }
});
