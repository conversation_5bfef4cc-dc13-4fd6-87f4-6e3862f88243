import { Message } from '../types/chat';
import { ChatSession, ChatSessionSummary } from '../types/chatSession';
import Cookies from 'js-cookie';
import { v4 as uuidv4 } from 'uuid';
import { API_BASE_URL } from '../config';

// Constants
const GUEST_SESSIONS_KEY = 'steel_converter_sessions';
const ACTIVE_SESSION_KEY = 'steel_converter_active_session';
const MAX_COOKIE_SIZE_BYTES = 4000; // Safe size for cookies

// Client-side cache for faster session switching
let sessionsCache: ChatSession[] = [];
let sessionCache: Record<string, ChatSession> = {};

// Function to check if user is logged in
const isLoggedIn = (): boolean => {
  return !!localStorage.getItem('token');
};

// Utilities for compression (basic version to conserve space)
const compressSessions = (sessions: ChatSession[]): string => {
  // Create a safe copy to avoid circular references
  try {
    // Only keep essential data for session list to save space
    const compressedSessions = sessions.map(session => {
      // Check for valid session structure
      if (!session || typeof session !== 'object') {
        console.warn('Invalid session object encountered:', session);
        return null;
      }

      // Safely extract messages
      const messages = Array.isArray(session.messages) ? session.messages : [];

      return {
        id: session.id || `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        title: session.title || 'Chat Session',
        lastUpdated: session.lastUpdated || new Date(),
        function: session.function || 'general',
        preview: session.preview || '',
        messages: messages.map(msg => {
          // Check for valid message structure
          if (!msg || typeof msg !== 'object') {
            console.warn('Invalid message object encountered:', msg);
            return {
              type: 'system',
              content: 'Invalid message data',
              timestamp: new Date(),
              function: 'general'
            };
          }

          // Safely extract result
          const result = msg.result && typeof msg.result === 'object' ? {
            converted_text: msg.result.converted_text || '',
            original_text: msg.result.original_text || '',
            hasTable: !!msg.result.hasTable,
            function: msg.result.function || 'general',
            from: msg.result.from || '',
            to: msg.result.to || '',
            value: msg.result.value || 0,
            converted_value: msg.result.converted_value || 0,
            formula: msg.result.formula || '',
            table_mode: !!msg.result.table_mode
          } : undefined;

          return {
            type: msg.type || 'system',
            content: typeof msg.content === 'string' ? msg.content : String(msg.content || ''),
            timestamp: msg.timestamp || new Date(),
            function: msg.function || 'general',
            isTable: !!msg.isTable,
            result: result,
            tableData: msg.tableData || undefined,
          };
        }).filter(Boolean), // Remove any null messages
      };
    }).filter(Boolean); // Remove any null sessions

    // Use a replacer function to handle any remaining circular references
    const seen = new WeakSet();
    return JSON.stringify(compressedSessions, (key, value) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular]';
        }
        seen.add(value);
      }
      return value;
    });
  } catch (error) {
    console.error('Error in compressSessions:', error);
    throw error;
  }
};

const decompressSessions = (data: string): ChatSession[] => {
  try {
    const parsed = JSON.parse(data);
    if (!Array.isArray(parsed)) return [];

    // Convert string timestamps back to Date objects
    return parsed.map(session => ({
      ...session,
      lastUpdated: new Date(session.lastUpdated),
      messages: session.messages.map((msg: any) => {
        // Determine isTable based on data presence
        const hasTableData = !!msg.tableData;
        const hasTableResult = msg.result && (msg.result.hasTable ||
          (msg.result.converted_text && msg.result.converted_text.includes('|')));

        return {
          ...msg,
          timestamp: new Date(msg.timestamp),
          // Ensure isTable flag is correctly restored
          isTable: msg.isTable || hasTableData || hasTableResult || false,
        };
      }),
    }));
  } catch (e) {
    console.error('Failed to decompress sessions:', e);
    return [];
  }
};

// Import IndexedDB functions
import {
  getAllChats,
  getChat,
  saveChat,
  deleteChat as deleteIndexedDBChat,
  addMessageToChat,
  setActiveChat as setIndexedDBActiveChat,
  getActiveChat as getIndexedDBActiveChat,
  debugIndexedDB
} from './indexedDBService';

// Local storage for guest users using IndexedDB
// This function handles saving all sessions as a whole to IndexedDB
const saveSessionsToLocal = async (sessions: ChatSession[]): Promise<void> => {
  try {
    console.log(`Saving ${sessions.length} sessions to IndexedDB`);

    // First, ensure the active session is saved
    const activeSessionId = await getActiveSession();
    if (activeSessionId) {
      const activeSession = sessions.find(s => s.id === activeSessionId) || sessionCache[activeSessionId];
      if (activeSession) {
        // Save active session to ensure it's always available
        await saveChat(activeSession);
      }
    }

    // Filter sessions to only include those with user messages or the active session
    // This reduces unnecessary IndexedDB operations
    const sessionsToSave = sessions.filter(session => {
      const hasUserMessages = session.messages.some(msg => msg.type === 'user');
      const isActiveSession = session.id === activeSessionId;
      return hasUserMessages || isActiveSession;
    });

    // Update cache with all sessions
    sessionsCache = sessions;
    sessions.forEach(session => {
      sessionCache[session.id] = session;
    });

    // Only save sessions with user messages or the active session
    // This is more efficient than saving all sessions
    for (const session of sessionsToSave) {
      await saveChat(session);
    }

    // Trigger a chat update event only after all sessions are saved
    window.dispatchEvent(new CustomEvent('chat-updated'));
  } catch (e) {
    console.error('Failed to save chat sessions to IndexedDB:', e);
  }
};

// Load all sessions from IndexedDB - only called on initial load or when needed
const loadSessionsFromLocal = async (): Promise<ChatSession[]> => {
  try {
    // Check if we already have sessions in cache to avoid unnecessary IndexedDB operations
    if (sessionsCache.length > 0) {
      console.log('Using cached sessions instead of loading from IndexedDB');
      return sessionsCache;
    }

    console.log('Loading sessions from IndexedDB');
    const allSessions = await getAllChats();
    console.log(`Loaded ${allSessions.length} sessions from IndexedDB`);

    // Validate sessions to ensure they have valid messages arrays
    const validSessions = allSessions.filter(session => {
      if (!session) {
        console.warn('Found null or undefined session in IndexedDB');
        return false;
      }

      if (!session.id) {
        console.warn('Found session without ID in IndexedDB');
        return false;
      }

      if (!session.messages || !Array.isArray(session.messages)) {
        console.warn(`Session ${session.id} has invalid messages property`);
        // Fix the session by adding an empty messages array
        session.messages = [];
      }

      return true;
    });

    console.log(`After validation, ${validSessions.length} valid sessions remain`);

    // Update cache for future use
    sessionsCache = validSessions;
    validSessions.forEach(session => {
      sessionCache[session.id] = session;
    });

    return validSessions;
  } catch (e) {
    console.error('Failed to load chat sessions from IndexedDB:', e);
    return [];
  }
};

// API calls for logged-in users
const saveSessionsToAPI = async (sessions: ChatSession[]): Promise<boolean> => {
  try {
    // Filter out sessions with no user messages and validate sessions
    const sessionsWithUserMessages = sessions.filter(session => {
      // Check if session and messages are valid
      if (!session || !session.messages || !Array.isArray(session.messages)) {
        console.warn(`Invalid session or messages array in session ${session?.id || 'unknown'}, skipping`);
        return false;
      }

      // Only include sessions with user messages
      return session.messages.some(msg => msg.type === 'user');
    });

    if (sessionsWithUserMessages.length === 0) {
      console.log('No sessions with user messages to save');
      return true; // Nothing to save, so consider it successful
    }

    console.log(`Saving ${sessionsWithUserMessages.length} sessions to API`);

    // Add a timeout to the fetch request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      // Use a safe stringify method to handle circular references
      const safeStringify = (obj: any) => {
        const seen = new WeakSet();
        return JSON.stringify(obj, (key, value) => {
          if (typeof value === 'object' && value !== null) {
            if (seen.has(value)) {
              return '[Circular]';
            }
            seen.add(value);
          }
          return value;
        });
      };

      // Prepare the request body with safe stringify
      let requestBody;

      // Ensure all sessions have valid messages arrays before stringifying
      const sanitizedSessions = sessionsWithUserMessages.map(session => {
        // Create a copy to avoid modifying the original
        const sanitized = { ...session };

        // Ensure messages is a valid array
        if (!sanitized.messages || !Array.isArray(sanitized.messages)) {
          console.warn(`Session ${sanitized.id} has invalid messages property, fixing before API call`);
          sanitized.messages = [];
        }

        // Ensure lastUpdated is a valid date
        if (!sanitized.lastUpdated || isNaN(new Date(sanitized.lastUpdated).getTime())) {
          console.warn(`Session ${sanitized.id} has invalid lastUpdated property, fixing before API call`);
          sanitized.lastUpdated = new Date();
        }

        return sanitized;
      });

      try {
        requestBody = JSON.stringify({ sessions: sanitizedSessions });
      } catch (stringifyError) {
        console.warn('Error stringifying sessions, using safe method:', stringifyError);
        requestBody = safeStringify({ sessions: sanitizedSessions });
      }

      const response = await fetch(`${API_BASE_URL}/chat/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: requestBody,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error saving sessions:', response.status, errorText);
        return false;
      }

      // Try to parse the response
      try {
        const data = await response.json();
        console.log('API save response:', data);
      } catch (parseError) {
        console.warn('Could not parse API response as JSON:', parseError);
      }

      return true;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      if (fetchError.name === 'AbortError') {
        console.error('API request timed out after 10 seconds');
      } else {
        console.error('Fetch error saving sessions:', fetchError);
      }
      throw fetchError; // Re-throw to be caught by the outer try/catch
    }
  } catch (e) {
    console.error('Failed to save chat sessions to API:', e);
    return false;
  }
};

// Load messages for a specific session from the API
const loadSessionMessagesFromAPI = async (sessionId: string): Promise<Message[]> => {
  try {
    console.log(`Loading messages for session ${sessionId} from API`);
    const response = await fetch(`${API_BASE_URL}/chat/messages/${sessionId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
    });

    if (!response.ok) {
      console.error(`API returned error when loading messages for session ${sessionId}:`, response.status, response.statusText);
      return [];
    }

    const data = await response.json();
    console.log(`API response for session ${sessionId} messages:`, data);

    // Check if the response has the expected structure
    if (data && data.success && data.data && Array.isArray(data.data.messages)) {
      // Map API response to Message objects
      return data.data.messages.map((msg: any) => ({
        type: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content || '',
        timestamp: new Date(msg.timestamp || new Date()),
        function: msg.function || 'chat',
        isTable: msg.is_table || false,
        tableData: msg.table_data || null,
        result: msg.result || null,
      }));
    } else {
      console.warn(`Unexpected API response format for session ${sessionId} messages:`, data);
      return [];
    }
  } catch (e) {
    console.error(`Failed to load messages for session ${sessionId} from API:`, e);
    return [];
  }
};

// Load all sessions from API with their messages
const loadSessionsFromAPI = async (): Promise<ChatSession[]> => {
  try {
    // Check if we already have sessions in cache to avoid unnecessary API calls
    if (sessionsCache.length > 0) {
      console.log('Using cached sessions instead of loading from API');
      return sessionsCache;
    }

    console.log('Loading sessions from API');
    const response = await fetch(`${API_BASE_URL}/chat/sessions`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
    });

    if (!response.ok) {
      console.error('API returned error:', response.status, response.statusText);
      return [];
    }

    const data = await response.json();
    console.log('API response:', data);

    // Check if the response has the expected structure
    if (data && data.success && data.data && Array.isArray(data.data.sessions)) {
      // Map API response to ChatSession objects with validation
      const sessionPromises = data.data.sessions
        .filter((session: any) => {
          if (!session || !session.session_id) {
            console.warn('Found invalid session in API response');
            return false;
          }
          return true;
        })
        .map(async (session: any) => {
          // Create basic session object
          const sessionObj: ChatSession = {
            id: session.session_id,
            title: session.title || 'Chat Session',
            messages: [], // Will be populated below
            lastUpdated: new Date(session.last_message_at || session.created_at || new Date()),
            function: 'chat',
            preview: '',
          };

          try {
            // Load messages for this session
            const messages = await loadSessionMessagesFromAPI(session.session_id);
            sessionObj.messages = messages;

            // Update preview from first user message if available
            const firstUserMessage = messages.find(msg => msg.type === 'user');
            if (firstUserMessage) {
              sessionObj.preview = firstUserMessage.content.substring(0, 50);
            }

            console.log(`Loaded ${messages.length} messages for session ${session.session_id}`);
          } catch (error) {
            console.error(`Failed to load messages for session ${session.session_id}:`, error);
            // Continue with empty messages array
          }

          return sessionObj;
        });

      // Wait for all sessions to load their messages
      const sessions = await Promise.all(sessionPromises);
      console.log(`Loaded ${sessions.length} sessions from API with their messages`);

      // Update cache for future use
      sessionsCache = sessions;
      sessions.forEach(session => {
        sessionCache[session.id] = session;
      });

      return sessions;
    } else {
      console.error('Unexpected API response format:', data);
      return [];
    }
  } catch (e) {
    console.error('Failed to load chat sessions from API:', e);
    return [];
  }
};

// Generate a title from the first message content
const generateTitleFromMessage = (message: string): string => {
  // Use first ~25 characters of message as title
  const title = message.trim().substring(0, 25);
  return title.length < message.length ? `${title}...` : title;
};

// Helper function to check if a session is in initial state (only has welcome message)
export const isInitialState = (session: ChatSession): boolean => {
  return session.messages.length === 1 &&
         session.messages[0].type === 'assistant' &&
         (session.messages[0].content.includes('欢迎使用') ||
          session.messages[0].content.includes('welcome'));
};

// Public API
export const createSession = async (initialMessage?: Message): Promise<ChatSession> => {
  console.log('Creating new session', initialMessage ? 'with initial message' : 'without initial message');

  // Always include a welcome message
  const welcomeMessage: Message = {
    type: 'assistant' as const,
    content: '欢迎使用钢铁单位转换器! 我可以帮您进行单位转换和回答钢铁行业相关问题。请输入您的问题或需要转换的内容。',
    timestamp: new Date()
  };

  // Use either the initial message if provided, or just the welcome message
  // Don't include both to avoid double welcome messages
  const messages = initialMessage && initialMessage.type !== 'assistant'
    ? [welcomeMessage, initialMessage]
    : [welcomeMessage];

  // Generate a unique ID for the new session
  const sessionId = uuidv4();
  console.log(`Generated new session ID: ${sessionId}`);

  const newSession: ChatSession = {
    id: sessionId,
    title: initialMessage && initialMessage.type === 'user'
      ? generateTitleFromMessage(initialMessage.content)
      : '新的转换',
    lastUpdated: new Date(),
    messages: messages,
    function: initialMessage?.function || 'general',
    preview: initialMessage?.type === 'user' ? initialMessage.content.substring(0, 50) : '新的会话',
  };

  console.log('New session created with ID:', newSession.id);

  // Only add to sessions list if it contains user messages
  // This ensures we don't save sessions that only have the welcome message
  const hasUserMessages = messages.some(msg => msg.type === 'user');

  // Always update the individual session cache immediately
  sessionCache[newSession.id] = newSession;

  // For non-logged-in users, always save the session to IndexedDB
  // This ensures we have the session available even if it's just a welcome message
  if (!isLoggedIn()) {
    console.log('User is not logged in, saving session to IndexedDB');
    await saveChat(newSession);
  }

  // Set this session as the active session immediately
  // This ensures the UI shows this session right away
  console.log('Setting new session as active:', newSession.id);
  await setActiveSession(newSession.id);

  if (hasUserMessages) {
    console.log('Session contains user messages, adding to history');
    // Get the current sessions list
    const sessions = await getSessions();
    console.log(`Retrieved ${sessions.length} existing sessions`);

    // Add the new session at the beginning
    sessions.unshift(newSession);
    console.log(`Added new session to sessions list, now has ${sessions.length} sessions`);

    // Save the updated sessions list
    if (isLoggedIn()) {
      console.log('User is logged in, saving session to API');
      const saveResult = await saveSessionsToAPI(sessions);
      if (!saveResult) {
        console.error('Failed to save new session to API');
        // Retry once after a short delay
        setTimeout(async () => {
          console.log('Retrying save to API...');
          const retryResult = await saveSessionsToAPI(sessions);
          console.log(`Retry result: ${retryResult ? 'success' : 'failed'}`);
        }, 1000);
      } else {
        console.log('Successfully saved new session to API');
      }
    } else {
      console.log('User is not logged in, saving sessions list to IndexedDB');
      await saveSessionsToLocal(sessions);
    }

    // Update sessions cache
    sessionsCache = [newSession, ...sessions.filter(s => s.id !== newSession.id)];
  } else {
    // For welcome-only sessions, don't add to the sessions list
    // but still keep in the cache for the current session
    console.log('Created initial welcome session (not added to history yet)');

    // Still update the sessions cache with this session
    const sessions = await getSessions();
    sessionsCache = sessions;
  }

  // Set this session as the active session
  console.log('Setting new session as active:', newSession.id);
  await setActiveSession(newSession.id);

  // Trigger a chat update event
  window.dispatchEvent(new CustomEvent('chat-updated'));

  // Debug IndexedDB state after creating session only in development mode
  if (process.env.NODE_ENV === 'development') {
    await debugIndexedDB();
  }

  return newSession;
};

export const getSessions = async (): Promise<ChatSession[]> => {
  let sessions: ChatSession[] = [];

  if (isLoggedIn()) {
    sessions = await loadSessionsFromAPI();
  } else {
    // Fixed: await the async function
    sessions = await loadSessionsFromLocal();
  }

  console.log(`getSessions: Retrieved ${sessions.length} sessions`);

  // Get the active session ID
  const activeSessionId = await getActiveSession();
  console.log(`Active session ID in getSessions: ${activeSessionId}`);

  // Filter sessions to include those with user messages AND the active session
  const filteredSessions = sessions.filter(session => {
    // Check if session and messages are valid
    if (!session || !session.messages || !Array.isArray(session.messages)) {
      console.warn(`Invalid session or messages array in session ${session?.id || 'unknown'}`);
      return false;
    }

    // Check if this session has any user messages
    const hasUserMessages = session.messages.some(msg => msg.type === 'user');
    const isActiveSession = session.id === activeSessionId;

    // Include if it has user messages OR it's the active session
    const shouldInclude = hasUserMessages || isActiveSession;

    if (!shouldInclude) {
      console.log(`Filtering out session ${session.id} - no user messages and not active session`);
    } else if (isActiveSession && !hasUserMessages) {
      console.log(`Including active session ${session.id} in getSessions even though it has no user messages`);
    }

    return shouldInclude;
  });

  console.log(`getSessions: After filtering, ${filteredSessions.length} sessions remain`);

  // Update cache
  sessionsCache = filteredSessions;
  filteredSessions.forEach(session => {
    sessionCache[session.id] = session;
  });

  return filteredSessions;
};

export const getSessionSummaries = async (): Promise<ChatSessionSummary[]> => {
  console.log('=== GET SESSION SUMMARIES DEBUG START ===');
  console.log('Getting session summaries');

  // First get all sessions to see what's available
  const allSessions = await getSessions();
  console.log(`Total sessions available: ${allSessions.length}`);
  allSessions.forEach(session => {
    const hasUserMessages = session.messages.some(msg => msg.type === 'user');
    console.log(`Session ${session.id}: ${session.title} - Has user messages: ${hasUserMessages}`);
  });

  // Get the active session ID
  const activeSessionId = await getActiveSession();
  console.log(`Active session ID: ${activeSessionId}`);

  // Filter sessions to include those with user messages AND the active session
  const sessions = allSessions.filter(session => {
    // Check if session and messages are valid
    if (!session || !session.messages || !Array.isArray(session.messages)) {
      console.warn(`Invalid session or messages array in session ${session?.id || 'unknown'}`);
      return false;
    }

    const hasUserMessages = session.messages.some(msg => msg.type === 'user');
    const isActiveSession = session.id === activeSessionId;

    // Include if it has user messages OR it's the active session
    const shouldInclude = hasUserMessages || isActiveSession;

    if (isActiveSession && !hasUserMessages) {
      console.log(`Including active session ${session.id} even though it has no user messages`);
    }

    return shouldInclude;
  });
  console.log(`After filtering, ${sessions.length} sessions remain`);

  // Log details about each session with user messages
  sessions.forEach(session => {
    console.log(`Session with user messages - ID: ${session.id}, Title: ${session.title}`);
    const userMessageCount = session.messages.filter(msg => msg.type === 'user').length;
    console.log(`User message count: ${userMessageCount}`);
  });

  // Include the full messages array in the summary
  const summaries = sessions.map(session => ({
    id: session.id,
    title: session.title,
    lastUpdated: session.lastUpdated,
    messageCount: session.messages.length,
    function: session.function,
    preview: session.preview,
    messages: session.messages, // Include the full messages array
  }));

  console.log(`Returning ${summaries.length} session summaries`);
  console.log('=== GET SESSION SUMMARIES DEBUG END ===');

  return summaries;
};

export const getSession = async (sessionId: string): Promise<ChatSession | null> => {
  // Return from cache if available
  if (sessionCache[sessionId]) {
    return sessionCache[sessionId];
  }

  const sessions = await getSessions();
  const session = sessions.find(session => session.id === sessionId) || null;

  // Update cache if found
  if (session) {
    sessionCache[sessionId] = session;
  }

  return session;
};

export const updateSession = async (
  sessionId: string,
  updates: Partial<Omit<ChatSession, 'id'>>
): Promise<ChatSession | null> => {
  const sessions = await getSessions();
  const sessionIndex = sessions.findIndex(session => session.id === sessionId);

  if (sessionIndex === -1) return null;

  // Update the session
  sessions[sessionIndex] = {
    ...sessions[sessionIndex],
    ...updates,
    lastUpdated: new Date(),
  };

  if (isLoggedIn()) {
    await saveSessionsToAPI(sessions);
  } else {
    await saveSessionsToLocal(sessions);
  }

  // Update cache
  sessionCache[sessionId] = sessions[sessionIndex];

  return sessions[sessionIndex];
};

export const deleteSession = async (sessionId: string): Promise<boolean> => {
  try {
    console.log(`Deleting session: ${sessionId}`);

    // First, check if the session exists in IndexedDB
    if (!isLoggedIn()) {
      try {
        // Delete from IndexedDB directly
        const deleted = await deleteIndexedDBChat(sessionId);
        console.log(`Deleted from IndexedDB: ${deleted}`);
      } catch (error) {
        console.error('Error deleting from IndexedDB:', error);
        // Continue with the process even if IndexedDB deletion fails
      }
    }

    // Get all sessions
    const sessions = await getSessions();
    console.log(`Found ${sessions.length} sessions before deletion`);

    const sessionIndex = sessions.findIndex(session => session.id === sessionId);
    if (sessionIndex === -1) {
      console.log(`Session ${sessionId} not found in sessions list`);
      return false;
    }

    // Remove the session
    sessions.splice(sessionIndex, 1);
    console.log(`Removed session from list, ${sessions.length} sessions remaining`);

    // Get the current active session
    const activeSessionId = await getActiveSession();
    console.log(`Current active session: ${activeSessionId}`);
    const wasActiveSession = activeSessionId === sessionId;
    console.log(`Was active session: ${wasActiveSession}`);

    // If we deleted the active session or there are no sessions left, create a new welcome session
    if (wasActiveSession || sessions.length === 0) {
      console.log('Creating new welcome session after deletion');

      // Clear the active session as we're going to create a new one
      await setActiveSession('');

      // Create a new welcome session (initial state)
      const newSession = await createInitialWelcomeSession();
      console.log('Created new welcome session after deletion with ID:', newSession.id);

      // Set it as the active session
      await setActiveSession(newSession.id);

      // Make sure it's not saved as history since it's just a welcome message
      // This ensures we return to the initial state with no history
      console.log('Ensuring new session is not saved as history');

      // Update cache with the new session
      sessionCache[newSession.id] = newSession;
    }
    // If there are other sessions and the active one was deleted, switch to the most recent one
    else if (wasActiveSession && sessions.length > 0) {
      // Sort by lastUpdated and pick the most recent one
      const sortedSessions = [...sessions].sort((a, b) =>
        b.lastUpdated.getTime() - a.lastUpdated.getTime()
      );
      await setActiveSession(sortedSessions[0].id);
      console.log('Switched to most recent session after deletion:', sortedSessions[0].id);
    }

    // Save updated sessions list
    console.log(`Saving updated sessions list with ${sessions.length} sessions`);
    if (isLoggedIn()) {
      await saveSessionsToAPI(sessions);
    } else {
      await saveSessionsToLocal(sessions);
    }

    // Update cache
    sessionsCache = sessions;
    delete sessionCache[sessionId];

    // Trigger a chat update event
    window.dispatchEvent(new CustomEvent('chat-updated'));

    return true;
  } catch (e) {
    console.error('Failed to delete session:', e);
    return false;
  }
};

export const saveMessage = async (
  sessionId: string,
  message: Message
): Promise<ChatSession | null> => {
  try {
    console.log(`Saving message to session ${sessionId}`);

    // Validate session ID
    if (!sessionId) {
      console.error('Cannot save message: No session ID provided');
      return null;
    }

    // First check the cache for faster response
    if (sessionCache[sessionId]) {
      // Create an updated session from cache for immediate UI update
      const cachedSession = sessionCache[sessionId];
      const updatedSession = {
        ...cachedSession,
        messages: [...cachedSession.messages, message],
        lastUpdated: new Date(),
        preview: message.type === 'user' ? message.content.substring(0, 50) : cachedSession.preview,
      };

      // Update cache immediately for responsive UI
      sessionCache[sessionId] = updatedSession;

      // Determine if this session should be saved to history
      const hasUserMessages = updatedSession.messages.some(msg => msg.type === 'user');
      const isUserMessage = message.type === 'user';
      const shouldSaveAsHistory = hasUserMessages || isUserMessage;

      // For non-logged-in users, save to IndexedDB
      if (!isLoggedIn()) {
        // Save asynchronously without blocking the UI
        setTimeout(async () => {
          try {
            // Save the session to IndexedDB
            await saveChat(updatedSession);

            // If this is a user message, update the sessions list
            if (shouldSaveAsHistory) {
              // Get all sessions from cache or IndexedDB
              let allSessions = sessionsCache.length > 0 ?
                sessionsCache :
                await getAllChats();

              // Update or add the session in the list
              const sessionIndex = allSessions.findIndex(s => s.id === sessionId);
              if (sessionIndex !== -1) {
                allSessions[sessionIndex] = updatedSession;
              } else {
                allSessions.push(updatedSession);
              }

              // Update the sessions cache
              sessionsCache = allSessions;

              // Save the updated sessions list to IndexedDB
              await saveSessionsToLocal(allSessions);

              // Make sure the active session ID is saved
              await setActiveSession(sessionId);
            }

            // Trigger a chat update event to refresh the sidebar
            window.dispatchEvent(new CustomEvent('chat-updated'));
          } catch (error) {
            console.error(`Error saving session ${sessionId} to IndexedDB:`, error);
          }
        }, 0);
      } else {
        // For logged-in users, save to API immediately
        // We'll still use a non-blocking approach to keep the UI responsive
        if (shouldSaveAsHistory) {
          console.log('User is logged in, saving session to API immediately');

          // Create a separate async function to handle the API call
          const saveToAPI = async () => {
            try {
              // Get all sessions
              const allSessions = await loadSessionsFromAPI();
              console.log(`Loaded ${allSessions.length} sessions from API for updating`);

              // Update or add the session in the list
              const sessionIndex = allSessions.findIndex(s => s.id === sessionId);
              if (sessionIndex !== -1) {
                console.log(`Updating existing session ${sessionId} in API sessions list`);
                allSessions[sessionIndex] = updatedSession;
              } else {
                console.log(`Adding new session ${sessionId} to API sessions list`);
                allSessions.push(updatedSession);
              }

              // Save to API and await the result
              console.log(`Saving ${allSessions.length} sessions to API`);
              const saveResult = await saveSessionsToAPI(allSessions);
              console.log(`Save result: ${saveResult ? 'success' : 'failed'}`);

              if (!saveResult) {
                console.error('Failed to save sessions to API');
              }
            } catch (error) {
              console.error('Error saving to API:', error);
            }
          };

          // Execute the async function without blocking
          saveToAPI();
        }
      }

      // Return the updated session immediately for UI responsiveness
      return updatedSession;
    } else {
      // If the session is not in cache, try to get it from storage
      console.log(`Session ${sessionId} not found in cache, trying to get from storage`);

      // Get the session from storage
      let session;
      if (!isLoggedIn()) {
        session = await getChat(sessionId);
      } else {
        session = await getSession(sessionId);
      }

      if (!session) {
        console.error(`Session ${sessionId} not found in storage`);
        return null;
      }

      // Update the session with the new message
      const updatedSession = {
        ...session,
        messages: [...session.messages, message],
        lastUpdated: new Date(),
        preview: message.type === 'user' ? message.content.substring(0, 50) : session.preview,
      };

      // Update cache
      sessionCache[sessionId] = updatedSession;

      // Save to storage asynchronously
      const hasUserMessages = updatedSession.messages.some(msg => msg.type === 'user');
      const isUserMessage = message.type === 'user';
      const shouldSaveAsHistory = hasUserMessages || isUserMessage;

      if (!isLoggedIn()) {
        setTimeout(async () => {
          try {
            await saveChat(updatedSession);

            if (shouldSaveAsHistory) {
              // Get all sessions
              const allSessions = await getAllChats();

              // Update or add the session
              const sessionIndex = allSessions.findIndex(s => s.id === sessionId);
              if (sessionIndex !== -1) {
                allSessions[sessionIndex] = updatedSession;
              } else {
                allSessions.push(updatedSession);
              }

              // Save the updated sessions list
              await saveSessionsToLocal(allSessions);

              // Make sure the active session ID is saved
              await setActiveSession(sessionId);
            }

            // Trigger a chat update event
            window.dispatchEvent(new CustomEvent('chat-updated'));
          } catch (error) {
            console.error(`Error saving session ${sessionId} to IndexedDB:`, error);
          }
        }, 0);
      } else {
        // For logged-in users, save to API immediately
        if (shouldSaveAsHistory) {
          console.log('User is logged in, saving session to API immediately');

          // Create a separate async function to handle the API call
          const saveToAPI = async () => {
            try {
              // Get all sessions
              const allSessions = await loadSessionsFromAPI();
              console.log(`Loaded ${allSessions.length} sessions from API for updating`);

              // Update or add the session in the list
              const sessionIndex = allSessions.findIndex(s => s.id === sessionId);
              if (sessionIndex !== -1) {
                console.log(`Updating existing session ${sessionId} in API sessions list`);
                allSessions[sessionIndex] = updatedSession;
              } else {
                console.log(`Adding new session ${sessionId} to API sessions list`);
                allSessions.push(updatedSession);
              }

              // Save to API and await the result
              console.log(`Saving ${allSessions.length} sessions to API`);
              const saveResult = await saveSessionsToAPI(allSessions);
              console.log(`Save result: ${saveResult ? 'success' : 'failed'}`);

              if (!saveResult) {
                console.error('Failed to save sessions to API');
              }
            } catch (error) {
              console.error('Error saving to API:', error);
            }
          };

          // Execute the async function without blocking
          saveToAPI();
        }
      }

      return updatedSession;
    }
  } catch (error) {
    console.error('Error in saveMessage:', error);

    // Even if there's an error, try to return the cached session if available
    if (sessionCache[sessionId]) {
      return sessionCache[sessionId];
    }

    return null;
  }
};

export const clearSessions = (): void => {
  localStorage.removeItem(GUEST_SESSIONS_KEY);
  localStorage.removeItem(ACTIVE_SESSION_KEY);

  // Clear cache
  sessionsCache = [];
  sessionCache = {};
};

// Sync IndexedDB chats to server when user logs in
export const syncIndexedDBToServer = async (): Promise<void> => {
  try {
    console.log('Syncing IndexedDB chats to server');

    // Only proceed if user is logged in
    if (!isLoggedIn()) {
      console.log('User not logged in, skipping sync');
      return;
    }

    // Get all chats from IndexedDB
    const localChats = await getAllChats();
    if (localChats.length === 0) {
      console.log('No local chats to sync');
      return;
    }

    console.log(`Found ${localChats.length} local chats to sync`);

    // Get all chats from server
    let serverChats: ChatSession[] = [];
    try {
      // This will now load all sessions with their messages
      serverChats = await loadSessionsFromAPI();
      console.log(`Found ${serverChats.length} server chats`);
    } catch (error) {
      console.error('Failed to load server chats:', error);
      // Continue with empty server chats
    }

    // Validate server chats
    const validServerChats = serverChats.filter(chat => {
      if (!chat || !chat.id) {
        console.warn('Found invalid server chat, skipping');
        return false;
      }

      // Ensure messages array exists
      if (!chat.messages || !Array.isArray(chat.messages)) {
        console.warn(`Server chat ${chat.id} has invalid messages array, fixing`);
        chat.messages = [];
      }

      return true;
    });

    // Determine if this is a new user (no server history)
    const isNewUser = validServerChats.length === 0;
    console.log(`User appears to be a ${isNewUser ? 'new' : 'returning'} user with ${validServerChats.length} server chats`);

    let mergedChats: ChatSession[] = [];

    if (isNewUser) {
      // For new users: Link all locally stored chat history to their account
      console.log('New user detected - linking all local chats to account');

      // Filter local chats to only include those with user messages
      const localChatsWithUserMessages = localChats.filter(chat => {
        // Check if chat and messages are valid
        if (!chat || !chat.messages || !Array.isArray(chat.messages)) {
          console.warn(`Invalid chat or messages array in chat ${chat?.id || 'unknown'}`);
          return false;
        }
        return chat.messages.some(msg => msg.type === 'user');
      });

      if (localChatsWithUserMessages.length > 0) {
        console.log(`Linking ${localChatsWithUserMessages.length} local chats to new user account`);
        mergedChats = [...localChatsWithUserMessages];
      } else {
        console.log('No local chats with user messages to link');
      }
    } else {
      // For existing users: Merge local and server chats with conflict resolution
      console.log('Existing user detected - merging local and server chats');

      // Start with all valid server chats
      mergedChats = [...validServerChats];

      // Process each local chat
      for (const localChat of localChats) {
        // Validate chat and messages
        if (!localChat || !localChat.messages || !Array.isArray(localChat.messages)) {
          console.warn(`Invalid local chat or messages array in chat ${localChat?.id || 'unknown'}, skipping`);
          continue;
        }

        // Only consider chats with user messages
        if (!localChat.messages.some(msg => msg.type === 'user')) {
          console.log(`Skipping local chat ${localChat.id} with no user messages`);
          continue;
        }

        // Check if this chat exists on server
        const serverChatIndex = mergedChats.findIndex(sc => sc.id === localChat.id);

        if (serverChatIndex === -1) {
          // Chat doesn't exist on server, add it
          console.log(`Adding new local chat ${localChat.id} to server`);
          mergedChats.push(localChat);
        } else {
          // Chat exists on both local and server, keep the most recently updated version
          let localUpdated = 0;
          let serverUpdated = 0;

          try {
            localUpdated = new Date(localChat.lastUpdated).getTime();
          } catch (error) {
            console.warn(`Invalid lastUpdated date for local chat ${localChat.id}, using current time`);
            localUpdated = new Date().getTime();
            localChat.lastUpdated = new Date();
          }

          try {
            serverUpdated = new Date(mergedChats[serverChatIndex].lastUpdated).getTime();
          } catch (error) {
            console.warn(`Invalid lastUpdated date for server chat ${mergedChats[serverChatIndex].id}, using current time`);
            serverUpdated = new Date().getTime();
            mergedChats[serverChatIndex].lastUpdated = new Date();
          }

          // Compare message counts as a secondary factor
          const localMessageCount = localChat.messages.length;
          const serverMessageCount = mergedChats[serverChatIndex].messages?.length || 0;

          // Log the message counts for debugging
          console.log(`Message counts - Local: ${localMessageCount}, Server: ${serverMessageCount}`);

          // Decide which version to keep
          if (localUpdated > serverUpdated) {
            console.log(`Updating server chat ${localChat.id} with newer local version (local: ${new Date(localUpdated).toISOString()}, server: ${new Date(serverUpdated).toISOString()})`);
            mergedChats[serverChatIndex] = localChat;
          } else if (localUpdated === serverUpdated && localMessageCount > serverMessageCount) {
            console.log(`Local and server timestamps match, but local has more messages (${localMessageCount} vs ${serverMessageCount}), using local version`);
            mergedChats[serverChatIndex] = localChat;
          } else {
            console.log(`Keeping server version of chat ${localChat.id} as it's newer or equal (server: ${new Date(serverUpdated).toISOString()}, local: ${new Date(localUpdated).toISOString()})`);

            // If server version has no messages but local does, use local messages
            if (serverMessageCount === 0 && localMessageCount > 0) {
              console.log(`Server version has no messages but local has ${localMessageCount}, using local messages with server metadata`);
              mergedChats[serverChatIndex].messages = localChat.messages;
            } else if (serverMessageCount > 0 && localMessageCount > 0) {
              // Both have messages, merge them if they're different
              console.log(`Both server and local have messages, checking if they need to be merged`);

              // Create a set of message fingerprints to identify unique messages
              const messageFingerprints = new Set();
              const mergedMessages: Message[] = [];

              // Add server messages first
              mergedChats[serverChatIndex].messages.forEach(msg => {
                // Create a fingerprint using content and timestamp
                const fingerprint = `${msg.type}:${msg.content}:${new Date(msg.timestamp).getTime()}`;
                messageFingerprints.add(fingerprint);
                mergedMessages.push(msg);
              });

              // Add local messages that don't exist in server
              let newMessagesAdded = 0;
              localChat.messages.forEach(msg => {
                const fingerprint = `${msg.type}:${msg.content}:${new Date(msg.timestamp).getTime()}`;
                if (!messageFingerprints.has(fingerprint)) {
                  mergedMessages.push(msg);
                  newMessagesAdded++;
                }
              });

              if (newMessagesAdded > 0) {
                console.log(`Added ${newMessagesAdded} unique messages from local chat to server chat`);

                // Sort messages by timestamp
                mergedMessages.sort((a, b) => {
                  return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
                });

                // Update the server chat with merged messages
                mergedChats[serverChatIndex].messages = mergedMessages;
              } else {
                console.log(`No new messages to add from local chat`);
              }
            }
          }
        }
      }
    }

    // Only proceed if we have chats to save
    if (mergedChats.length === 0) {
      console.log('No chats to sync after merging');
      return;
    }

    // Save merged chats to server
    console.log(`Saving ${mergedChats.length} chats to server`);
    await saveSessionsToAPI(mergedChats);
    console.log(`Successfully synced ${mergedChats.length} chats to server`);

    // Update cache with the merged chats
    sessionsCache = mergedChats;
    mergedChats.forEach(chat => {
      sessionCache[chat.id] = chat;
    });

    // Preserve the active session
    try {
      const activeSessionId = await getActiveSession();
      if (activeSessionId) {
        console.log(`Preserving active session: ${activeSessionId}`);

        // Check if active session is already in the merged chats
        const isActiveSessionInMergedChats = mergedChats.some(chat => chat.id === activeSessionId);

        if (isActiveSessionInMergedChats) {
          console.log(`Active session ${activeSessionId} is already in merged chats`);
        } else {
          // Make sure the active session is in the cache
          if (sessionCache[activeSessionId]) {
            console.log(`Active session ${activeSessionId} found in cache`);
            const cachedSession = sessionCache[activeSessionId];

            // Add to server if it has user messages
            if (cachedSession.messages && Array.isArray(cachedSession.messages) &&
                cachedSession.messages.some(msg => msg.type === 'user')) {
              console.log(`Adding active session ${activeSessionId} from cache to server`);
              mergedChats.push(cachedSession);
              await saveSessionsToAPI(mergedChats);
            } else {
              console.log(`Active session ${activeSessionId} from cache has no user messages, not adding to server`);
            }
          } else {
            // If not in cache, try to get it from IndexedDB
            try {
              const activeSession = await getChat(activeSessionId);
              if (activeSession) {
                console.log(`Retrieved active session ${activeSessionId} from IndexedDB`);
                sessionCache[activeSessionId] = activeSession;

                // Add to server if it has user messages
                if (activeSession.messages && Array.isArray(activeSession.messages) &&
                    activeSession.messages.some(msg => msg.type === 'user')) {
                  console.log(`Adding active session ${activeSessionId} from IndexedDB to server`);
                  mergedChats.push(activeSession);
                  await saveSessionsToAPI(mergedChats);
                } else {
                  console.log(`Active session ${activeSessionId} from IndexedDB has no user messages, not adding to server`);
                }
              } else {
                console.log(`Active session ${activeSessionId} not found in IndexedDB`);
              }
            } catch (error) {
              console.error(`Error retrieving active session ${activeSessionId} from IndexedDB:`, error);
            }
          }
        }
      } else {
        console.log('No active session to preserve');
      }
    } catch (error) {
      console.error('Error preserving active session:', error);
    }

    // Trigger a chat update event
    window.dispatchEvent(new CustomEvent('chat-updated'));
  } catch (error) {
    console.error('Failed to sync IndexedDB chats to server:', error);
  }
};

// Active session management - optimized to reduce IndexedDB operations
export const setActiveSession = async (sessionId: string): Promise<void> => {
  console.log(`Setting active session: ${sessionId}`);

  // Save to localStorage for immediate access
  localStorage.setItem(ACTIVE_SESSION_KEY, sessionId);

  try {
    // Save the active session ID to IndexedDB asynchronously
    setTimeout(async () => {
      try {
        // Save active session ID to IndexedDB
        await setIndexedDBActiveChat(sessionId);

        // Make sure the session exists in IndexedDB for non-logged-in users
        if (sessionId && !isLoggedIn() && sessionCache[sessionId]) {
          await saveChat(sessionCache[sessionId]);
        }
      } catch (error) {
        console.error('Error saving active session to IndexedDB:', error);
      }
    }, 0);
  } catch (error) {
    console.error('Error in setActiveSession:', error);
  }

  // Trigger a chat update event to refresh the sidebar
  window.dispatchEvent(new CustomEvent('chat-updated'));
};

export const getActiveSession = async (): Promise<string> => {
  try {
    // First check localStorage for immediate response
    const localStorageId = localStorage.getItem(ACTIVE_SESSION_KEY);
    if (localStorageId) {
      // Save to IndexedDB asynchronously for future use
      setTimeout(async () => {
        try {
          await setIndexedDBActiveChat(localStorageId);
        } catch (error) {
          console.warn('Failed to save active session ID to IndexedDB:', error);
        }
      }, 0);

      return localStorageId;
    }

    // If not in localStorage, try IndexedDB
    try {
      const activeId = await getIndexedDBActiveChat();
      if (activeId) {
        // Update localStorage for faster access next time
        localStorage.setItem(ACTIVE_SESSION_KEY, activeId);
        return activeId;
      }
    } catch (error) {
      console.warn('Failed to get active session from IndexedDB:', error);
    }

    return '';
  } catch (error) {
    console.error('Error in getActiveSession:', error);
    return '';
  }
};

// For initial app load - always ensures there's an active session
export const getOrCreateActiveSession = async (): Promise<ChatSession> => {
  try {
    console.log('Attempting to get or create active session');

    // First try to get the active session ID from IndexedDB
    let activeSessionId;
    try {
      // Try to get from IndexedDB first
      activeSessionId = await getIndexedDBActiveChat();
      if (activeSessionId) {
        console.log('Active session ID from IndexedDB:', activeSessionId);
      } else {
        // Fall back to localStorage
        activeSessionId = localStorage.getItem(ACTIVE_SESSION_KEY);
        console.log('Active session ID from localStorage:', activeSessionId);

        // If found in localStorage but not in IndexedDB, save it to IndexedDB
        if (activeSessionId) {
          await setIndexedDBActiveChat(activeSessionId);
          console.log('Saved active session ID from localStorage to IndexedDB');
        }
      }
    } catch (error) {
      console.warn('Failed to get active session ID, will create new session:', error);
      activeSessionId = null;
    }

    // If we have an active session ID, try to get the session
    if (activeSessionId) {
      try {
        // First try to get from IndexedDB directly for non-logged-in users
        let session = null;
        if (!isLoggedIn()) {
          try {
            session = await getChat(activeSessionId);
            console.log('Found session in IndexedDB:', activeSessionId);
          } catch (error) {
            console.warn(`Failed to get session ${activeSessionId} from IndexedDB:`, error);
          }
        }

        // If not found in IndexedDB or user is logged in, try the normal getSession
        if (!session) {
          session = await getSession(activeSessionId);
        }

        if (session) {
          // Ensure session is in the cache
          sessionCache[activeSessionId] = session;
          console.log('Found existing active session:', activeSessionId);
          return session;
        }
        console.warn(`Active session ID ${activeSessionId} exists but session not found`);
      } catch (error) {
        console.warn(`Failed to get session for ID ${activeSessionId}:`, error);
      }
    }

    // If we get here, we need to create a new session
    // First check if there are any existing sessions we can use
    try {
      const sessions = await getSessions();
      if (sessions.length > 0) {
        // Use the most recent session
        const mostRecentSession = [...sessions].sort((a, b) =>
          b.lastUpdated.getTime() - a.lastUpdated.getTime()
        )[0];

        console.log('Using most recent session:', mostRecentSession.id);
        await setActiveSession(mostRecentSession.id);
        sessionCache[mostRecentSession.id] = mostRecentSession;
        return mostRecentSession;
      }
    } catch (error) {
      console.warn('Failed to get existing sessions:', error);
    }

    // If no active session or it doesn't exist, create a new welcome session (initial state)
    console.log('Creating new welcome session as no active session was found');
    const newSession = await createInitialWelcomeSession();
    console.log('Created new welcome session with ID:', newSession.id);

    // Make sure to set this as the active session
    await setActiveSession(newSession.id);

    return newSession;
  } catch (error) {
    console.error('Critical error in getOrCreateActiveSession:', error);

    // Last resort fallback - create a session without saving it
    const fallbackSession: ChatSession = {
      id: uuidv4(),
      title: '新的转换',
      lastUpdated: new Date(),
      messages: [{
        type: 'assistant',
        content: '欢迎使用钢铁单位转换器! 我可以帮您进行单位转换和回答钢铁行业相关问题。请输入您的问题或需要转换的内容。',
        timestamp: new Date()
      }],
      function: 'general',
      preview: '新的会话'
    };

    console.log('Created fallback session:', fallbackSession.id);
    sessionCache[fallbackSession.id] = fallbackSession;

    // Try to save the fallback session
    try {
      await saveChat(fallbackSession);
      await setActiveSession(fallbackSession.id);
    } catch (saveError) {
      console.error('Failed to save fallback session:', saveError);
    }

    return fallbackSession;
  }
};

// Create an initial welcome session (used when no session is selected or on first load)
export const createInitialWelcomeSession = async (): Promise<ChatSession> => {
  console.log('Creating initial welcome session');

  const welcomeMessage: Message = {
    type: 'assistant' as const,
    content: '欢迎使用钢铁单位转换器! 我可以帮您进行单位转换和回答钢铁行业相关问题。请输入您的问题或需要转换的内容。',
    timestamp: new Date(),
  };

  // Create a new session directly
  const newSession: ChatSession = {
    id: uuidv4(),
    title: '新的转换',
    lastUpdated: new Date(),
    messages: [welcomeMessage],
    function: 'general',
    preview: '新的会话'
  };

  // Add to cache immediately for responsive UI
  sessionCache[newSession.id] = newSession;

  // Set this as the active session in localStorage for immediate access
  localStorage.setItem(ACTIVE_SESSION_KEY, newSession.id);

  // For non-logged-in users, save to IndexedDB asynchronously
  if (!isLoggedIn()) {
    setTimeout(async () => {
      try {
        // Save the session to IndexedDB
        await saveChat(newSession);

        // Save the active session ID to IndexedDB
        await setIndexedDBActiveChat(newSession.id);

        // Update the sessions list asynchronously
        // Use cached sessions if available to avoid unnecessary IndexedDB operations
        const allSessions = sessionsCache.length > 0 ?
          [...sessionsCache] :
          await getAllChats();

        // Check if the new session is already in the list
        if (!allSessions.some(s => s.id === newSession.id)) {
          allSessions.push(newSession);
          // Update the sessions cache
          sessionsCache = allSessions;
          // Save the updated sessions list to IndexedDB
          await saveSessionsToLocal(allSessions);
        }
      } catch (error) {
        console.error('Failed to save initial welcome session to IndexedDB:', error);
      }
    }, 0);
  }

  // Trigger a chat update event
  window.dispatchEvent(new CustomEvent('chat-updated'));

  return newSession;
};

// Force save a session if it meets criteria for being worthy of saving
export const saveSessionIfNeeded = async (sessionId: string): Promise<boolean> => {
  console.log(`Checking if session ${sessionId} needs to be saved`);
  const session = await getSession(sessionId);
  if (!session) {
    console.log(`Session ${sessionId} not found, cannot save`);
    return false;
  }

  // Check if this session has any user messages
  const hasUserMessages = session.messages.some(msg => msg.type === 'user');
  console.log(`Session ${sessionId} has user messages: ${hasUserMessages}`);

  // Always save a session that has at least one user message
  // This is the key criteria - if the user has sent at least one message, we should save the chat
  const shouldSaveAsHistory = hasUserMessages;

  // Only save if it meets the criteria for being real history
  if (shouldSaveAsHistory) {
    console.log(`Session ${sessionId} should be saved as history`);

    // Get fresh sessions list but ensure this session is updated with latest data
    const sessions = await getSessions();
    console.log(`Retrieved ${sessions.length} existing sessions`);

    // Check if this session is already in the list
    const sessionIndex = sessions.findIndex(s => s.id === sessionId);
    console.log(`Session index in list: ${sessionIndex}`);

    if (sessionIndex !== -1) {
      // Update with the latest from cache if available
      if (sessionCache[sessionId]) {
        console.log(`Updating existing session ${sessionId} in sessions list`);
        sessions[sessionIndex] = sessionCache[sessionId];
      }
    } else {
      // If this is a new session with user messages, add it to the sessions list
      if (sessionCache[sessionId]) {
        console.log(`Adding new session ${sessionId} to sessions list`);
        sessions.unshift(sessionCache[sessionId]); // Add to the beginning of the list
      }
    }

    // Save the sessions list with the updated session
    console.log(`Saving updated sessions list with ${sessions.length} sessions`);
    if (isLoggedIn()) {
      await saveSessionsToAPI(sessions);
    } else {
      await saveSessionsToLocal(sessions);
    }

    // Debug the current state of IndexedDB only in development mode
    if (process.env.NODE_ENV === 'development') {
      await debugIndexedDB();
    }

    // Trigger a chat update event
    window.dispatchEvent(new CustomEvent('chat-updated'));

    console.log(`Saved session ${sessionId} as history - has user messages`);
    return true;
  }

  console.log(`Not saving session ${sessionId} as history - no user messages`);
  return false;
};

// Add a helper function to ensure messages are bound to the correct session
const ensureMessageInCorrectSession = async (sessionId: string, message: Message): Promise<void> => {
  try {
    // Verify that the session exists
    const session = await getSession(sessionId);
    if (!session) {
      console.error('Session not found:', sessionId);
      return;
    }

    // Log for debugging
    console.log(`Saving message to session ${sessionId}:`, message.type);

    // Add the message to the session
    const updatedSession = await saveMessage(sessionId, message);
    if (!updatedSession) {
      console.error('Failed to save message to session:', sessionId);
    }
  } catch (error) {
    console.error('Error ensuring message in correct session:', error);
  }
};