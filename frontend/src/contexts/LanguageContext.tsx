import React, { createContext, useState, useContext, useEffect } from 'react';

// Define available languages
export type Language = 'zh' | 'en';

// Define translations
export const translations = {
  zh: {
    // App
    appName: '钢铁智联',
    tagline: '专业钢材单位转换服务',

    // Conversion modes
    imperialToMetric: '英制 → 公制',
    metricToImperial: '公制 → 英制',

    // Actions
    convert: '单位转换',
    table: '制表',
    send: '发送',
    sending: '发送中...',
    cancel: '取消',

    // Welcome messages
    welcome: '欢迎来到钢铁智联！给STEELNET发送消息，请输入你想转换的钢材数据，例如: 0.75" x 10" x 5\'。',

    // Input placeholders
    inputPlaceholder: '输入钢材数据，例如：0.75" x 10" x 5\'...',

    // Result actions
    copy: '复制',
    like: '点赞',
    reportError: '报告错误',
    feedback: '反馈建议',
    sendToEmail: '发送到邮箱',
    downloadExcel: '下载Excel',

    // Email
    emailTitle: '发送转换结果到邮箱',
    emailDescription: '我们将把转换结果发送到您的邮箱，方便您后续查看和使用。',
    emailAddress: '邮箱地址',
    emailPreview: '预览内容',
    emailSuccess: '发送成功！邮件已发出',
    emailError: '发送失败，请稍后再试',

    // Table headers
    steelType: '钢铁类型',
    length: '长度',
    width: '宽度',
    thickness: '厚度',
    weight: '重量',

    // Notifications
    conversionComplete: '转换完成！以下是您的转换结果：',
    conversionFailed: '转换失败，请检查您的输入内容或网络连接。',
    conversionError: '抱歉，转换过程中发生错误。请稍后再试或联系支持团队。',

    // Feedback
    feedbackThanks: '感谢您的反馈！我们将努力改进我们的服务。',
    errorReportThanks: '感谢您报告错误！我们将尽快修复。',

    // Authentication
    login: '登录',
    register: '注册',
    email: '邮箱',
    password: '密码',
    confirmPassword: '确认密码',
    username: '用户名',
    usernameHelp: '请输入您的姓名或昵称',
    companyName: '公司名称',
    companyNameHelp: '请输入您的公司或组织名称',
    country: '国家/地区',
    required: '必填',
    optional: '选填',
    passwordHelp: '密码至少需要6个字符，包含大小写字母和数字',
    passwordsMustMatch: '两次输入的密码不匹配',
    invalidEmail: '邮箱格式不正确',
    verificationCode: '验证码',
    verificationSent: '验证码已发送至您的邮箱',
    verificationFailed: '验证失败',
    verificationSuccess: '邮箱验证成功，请完善您的个人信息',
    loggingIn: '正在登录...',
    syncingChats: '正在同步聊天记录...',
    loginSuccess: '登录成功',
    registerSuccess: '注册成功',
    loginFailed: '登录失败，请检查用户名和密码',
    registerFailed: '注册失败',
    hasAccount: '已有账号？点击登录',
    noAccount: '没有账号？点击注册',
    completeRegistration: '完成注册',
    nextStep: '下一步',
    profile: '个人资料',
    verification: '验证',
    codeSent: '验证码已发送',
    phoneNumber: '电话号码',
    address: '地址',
    city: '城市',
    postalCode: '邮政编码',
    accountType: '账户类型',
    individual: '个人',
    business: '企业',
    logout: '登出',
    logoutConfirm: '确定要登出吗？',
    profileUpdate: '更新个人资料',
    changePassword: '修改密码',
    currentPassword: '当前密码',
    newPassword: '新密码',
    close: '关闭',
    success: '成功',

    // Password Reset
    forgotPassword: '忘记密码？',
    resetPassword: '重置密码',
    resetPasswordInstructions: '请输入您的注册邮箱，我们将向您发送密码重置验证码。',
    sendResetCode: '发送重置验证码',
    resetCodeSent: '重置验证码已发送到您的邮箱。',
    enterVerificationCodeAndNewPassword: '请输入您收到的验证码和新密码。',
    passwordResetSuccess: '密码重置成功！您现在可以使用新密码登录。',
    backToLogin: '返回登录',
    emailRequired: '请输入邮箱地址',
    verificationCodeRequired: '请输入验证码',
    newPasswordRequired: '请输入新密码',
    passwordsDoNotMatch: '两次输入的密码不匹配',
    passwordTooShort: '密码长度至少为8个字符',
    resetRequestFailed: '密码重置请求失败',
    resetConfirmFailed: '密码重置确认失败',
    requestResetCode: '请求重置验证码',
    verifyAndReset: '验证并重置'
  },
  en: {
    // App
    appName: 'SteelNet',
    tagline: 'Professional Steel Unit Conversion Service',

    // Conversion modes
    imperialToMetric: 'Imperial → Metric',
    metricToImperial: 'Metric → Imperial',

    // Actions
    convert: 'Convert',
    table: 'Table',
    send: 'Send',
    sending: 'Sending...',
    cancel: 'Cancel',

    // Welcome messages
    welcome: 'Welcome to SteelNet! Please send us your steel data for conversion, for example: 0.75" x 10" x 5\'.',

    // Input placeholders
    inputPlaceholder: 'Enter steel data, e.g.: 0.75" x 10" x 5\'...',

    // Result actions
    copy: 'Copy',
    like: 'Like',
    reportError: 'Report Error',
    feedback: 'Feedback',
    sendToEmail: 'Send to Email',
    downloadExcel: 'Download Excel',

    // Email
    emailTitle: 'Send Conversion Result to Email',
    emailDescription: 'We will send the conversion result to your email for future reference.',
    emailAddress: 'Email Address',
    emailPreview: 'Preview',
    emailSuccess: 'Success! Email has been sent',
    emailError: 'Sending failed, please try again later',

    // Table headers
    steelType: 'Steel Type',
    length: 'Length',
    width: 'Width',
    thickness: 'Thickness',
    weight: 'Weight',

    // Notifications
    conversionComplete: 'Conversion complete! Here are your results:',
    conversionFailed: 'Conversion failed. Please check your input or network connection.',
    conversionError: 'Sorry, an error occurred during the conversion. Please try again later or contact support.',

    // Feedback
    feedbackThanks: 'Thank you for your feedback! We will work to improve our service.',
    errorReportThanks: 'Thank you for reporting this error! We will fix it as soon as possible.',

    // Authentication
    login: 'Login',
    register: 'Register',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    username: 'Username',
    usernameHelp: 'Please enter your name or nickname',
    companyName: 'Company Name',
    companyNameHelp: 'Please enter your company or organization name',
    country: 'Country/Region',
    required: 'Required',
    optional: 'Optional',
    passwordHelp: 'Password must be at least 6 characters, including uppercase, lowercase letters and numbers',
    passwordsMustMatch: 'Passwords must match',
    invalidEmail: 'Invalid email format',
    verificationCode: 'Verification Code',
    verificationSent: 'Verification code has been sent to your email',
    verificationFailed: 'Verification failed',
    verificationSuccess: 'Email verified successfully, please complete your profile',
    loggingIn: 'Logging in...',
    syncingChats: 'Syncing chat history...',
    loginSuccess: 'Login successful',
    registerSuccess: 'Registration successful',
    loginFailed: 'Login failed, please check your credentials',
    registerFailed: 'Registration failed',
    hasAccount: 'Already have an account? Login',
    noAccount: 'Don\'t have an account? Register',
    completeRegistration: 'Complete Registration',
    nextStep: 'Next',
    profile: 'Profile',
    verification: 'Verification',
    codeSent: 'Code sent',
    phoneNumber: 'Phone Number',
    address: 'Address',
    city: 'City',
    postalCode: 'Postal Code',
    accountType: 'Account Type',
    individual: 'Individual',
    business: 'Business',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',
    profileUpdate: 'Update Profile',
    changePassword: 'Change Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    close: 'Close',
    success: 'Success',

    // Password Reset
    forgotPassword: 'Forgot Password?',
    resetPassword: 'Reset Password',
    resetPasswordInstructions: 'Please enter your registered email, we will send you a password reset verification code.',
    sendResetCode: 'Send Reset Code',
    resetCodeSent: 'Reset verification code has been sent to your email.',
    enterVerificationCodeAndNewPassword: 'Please enter the verification code and new password you received.',
    passwordResetSuccess: 'Password reset successful! You can now use the new password to login.',
    backToLogin: 'Back to Login',
    emailRequired: 'Please enter your email address',
    verificationCodeRequired: 'Please enter the verification code',
    newPasswordRequired: 'Please enter the new password',
    passwordsDoNotMatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 8 characters',
    resetRequestFailed: 'Password reset request failed',
    resetConfirmFailed: 'Password reset confirmation failed',
    requestResetCode: 'Request Reset Code',
    verifyAndReset: 'Verify and Reset'
  }
};

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: keyof typeof translations['zh']) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get initially preferred language from localStorage or browser language
  const getInitialLanguage = (): Language => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'zh' || savedLanguage === 'en')) {
      return savedLanguage;
    }

    // Browser language detection
    const browserLang = navigator.language.split('-')[0];
    return browserLang === 'zh' ? 'zh' : 'en';
  };

  const [language, setLanguageState] = useState<Language>(getInitialLanguage);

  // Translator function
  const t = (key: keyof typeof translations['zh']): string => {
    return translations[language][key] || key;
  };

  // Set language and save to localStorage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('language', lang);
  };

  const value = {
    language,
    setLanguage,
    t,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;