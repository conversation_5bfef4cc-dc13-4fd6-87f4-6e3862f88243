import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { ChatSession, ChatSessionSummary } from '../types/chatSession';
import { Message } from '../types/chat';
import {
  getSessionSummaries,
  getSession,
  saveMessage,
  createSession,
  deleteSession,
  updateSession,
  getActiveSession,
  setActiveSession,
} from '../services/chatSessionStorage';

interface ChatContextType {
  sessions: ChatSessionSummary[];
  currentSession: ChatSession | null;
  loading: boolean;
  sendMessage: (message: Message) => Promise<void>;
  createNewChat: () => Promise<void>;
  deleteChat: (sessionId: string) => Promise<void>;
  switchChat: (sessionId: string) => Promise<void>;
  refreshSessions: () => Promise<void>;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sessions, setSessions] = useState<ChatSessionSummary[]>([]);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Load sessions and current session
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      // Get all sessions
      const loadedSessions = await getSessionSummaries();
      setSessions(loadedSessions);

      // Get active session
      const activeSessionId = getActiveSession();
      if (activeSessionId) {
        const session = await getSession(activeSessionId);
        setCurrentSession(session);
      }
    } catch (error) {
      console.error('Failed to load chat data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Refresh sessions
  const refreshSessions = async () => {
    await loadData();
  };

  // Send a message
  const sendMessage = async (message: Message) => {
    if (!currentSession) return;

    try {
      // Save message to current session
      const updatedSession = await saveMessage(currentSession.id, message);
      if (updatedSession) {
        setCurrentSession(updatedSession);
        
        // Trigger a chat update event
        window.dispatchEvent(new CustomEvent('chat-updated'));
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  // Create a new chat
  const createNewChat = async () => {
    try {
      // Create a new session
      const newSession = await createSession();
      setCurrentSession(newSession);
      
      // Refresh sessions
      await refreshSessions();
      
      // Trigger a chat update event
      window.dispatchEvent(new CustomEvent('chat-updated'));
    } catch (error) {
      console.error('Failed to create new chat:', error);
    }
  };

  // Delete a chat
  const deleteChat = async (sessionId: string) => {
    try {
      // Delete the session
      await deleteSession(sessionId);
      
      // Refresh sessions
      await refreshSessions();
      
      // If the deleted session was the current one, load the new active session
      if (currentSession?.id === sessionId) {
        const activeSessionId = getActiveSession();
        if (activeSessionId) {
          const session = await getSession(activeSessionId);
          setCurrentSession(session);
        } else {
          setCurrentSession(null);
        }
      }
      
      // Trigger a chat update event
      window.dispatchEvent(new CustomEvent('chat-updated'));
    } catch (error) {
      console.error('Failed to delete chat:', error);
    }
  };

  // Switch to a different chat
  const switchChat = async (sessionId: string) => {
    try {
      // Set the active session
      setActiveSession(sessionId);
      
      // Load the session
      const session = await getSession(sessionId);
      setCurrentSession(session);
    } catch (error) {
      console.error('Failed to switch chat:', error);
    }
  };

  return (
    <ChatContext.Provider
      value={{
        sessions,
        currentSession,
        loading,
        sendMessage,
        createNewChat,
        deleteChat,
        switchChat,
        refreshSessions,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
