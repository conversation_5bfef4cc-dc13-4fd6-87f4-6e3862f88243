import React from 'react';
import { Icon<PERSON>utton, <PERSON>u, <PERSON>uI<PERSON>, Tooltip, useTheme, Box } from '@mui/material';
import LanguageIcon from '@mui/icons-material/Language';
import { useLanguage, Language } from '../contexts/LanguageContext';

interface LanguageSwitcherProps {
  fixed?: boolean;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ fixed = false }) => {
  const { language, setLanguage } = useLanguage();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const theme = useTheme();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageSelect = (lang: Language) => {
    setLanguage(lang);
    handleClose();
  };

  return (
    <Box sx={fixed ? {
      position: 'fixed',
      top: 16,
      right: 16,
      zIndex: theme.zIndex.drawer - 1
    } : {}}>
      <Tooltip title={language === 'zh' ? 'Change Language' : '切换语言'}>
        <IconButton
          onClick={handleClick}
          size="small"
          sx={{
            color: theme.palette.mode === 'dark' ? 'white' : 'inherit',
            bgcolor: 'rgba(255, 255, 255, 0.8)',
            boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.9)',
            }
          }}
        >
          <LanguageIcon />
        </IconButton>
      </Tooltip>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem 
          selected={language === 'zh'} 
          onClick={() => handleLanguageSelect('zh')}
        >
          中文
        </MenuItem>
        <MenuItem 
          selected={language === 'en'} 
          onClick={() => handleLanguageSelect('en')}
        >
          English
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default LanguageSwitcher; 