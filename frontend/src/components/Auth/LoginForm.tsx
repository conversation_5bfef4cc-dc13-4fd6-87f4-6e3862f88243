import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  Link,
  CircularProgress,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { authApi } from '../../services/api';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';

interface LoginFormProps {
  onToggleForm: () => void;
  onLoginSuccess: (token: string, username: string) => void;
  onClose: () => void;
  onForgotPassword?: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onToggleForm,
  onLoginSuccess,
  onClose,
  onForgotPassword
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<string>('');
  const [error, setError] = useState('');
  const { t, language } = useLanguage();
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setError(t('loginFailed'));
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(t('invalidEmail'));
      return;
    }

    setLoading(true);
    setError('');
    setStatus(t('loggingIn'));

    try {
      // First, use the authApi to get the token
      const response = await authApi.login(email, password);
      console.log('Login successful:', response);

      // Update status to inform user about chat history sync
      setStatus(t('syncingChats'));

      // Then, use the login function from AuthContext to update the user state
      // This will also handle syncing chat history
      await login(email, password);

      // Update status to indicate success
      setStatus(t('loginSuccess'));

      // Call the onLoginSuccess callback
      onLoginSuccess(response.access_token, response.username);

      // Add a small delay before reloading to ensure token is saved
      // and to let the user see the success message
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (err: any) {
      console.error('Login error:', err.response || err);
      setError(err.response?.data?.detail || t('loginFailed'));
      setStatus('');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 400, mx: 'auto', mt: 4, position: 'relative' }}>
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: 8,
          top: 8,
        }}
      >
        <CloseIcon />
      </IconButton>

      <Typography variant="h5" gutterBottom align="center">
        {t('login')}
      </Typography>

      <Box component="form" noValidate onSubmit={handleSubmit} sx={{ mt: 2 }}>
        <TextField
          fullWidth
          label={t('email')}
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          margin="normal"
          required
          placeholder={t('email')}
          error={!!error && !email}
          disabled={loading}
        />

        <TextField
          fullWidth
          label={t('password')}
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          margin="normal"
          required
          placeholder={t('password')}
          error={!!error && !password}
          disabled={loading}
        />

        {error && (
          <Typography color="error" sx={{ mt: 2 }}>
            {error}
          </Typography>
        )}

        {status && !error && (
          <Typography color="primary" sx={{ mt: 2, textAlign: 'center' }}>
            {status}
          </Typography>
        )}

        <Button
          type="submit"
          fullWidth
          variant="contained"
          sx={{ mt: 3, mb: 2 }}
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : t('login')}
        </Button>

        <Box sx={{ textAlign: 'center', display: 'flex', flexDirection: 'column', gap: 1 }}>
          {onForgotPassword && (
            <Link
              component="button"
              variant="body2"
              onClick={onForgotPassword}
              sx={{ cursor: 'pointer' }}
              disabled={loading}
            >
              {t('forgotPassword')}
            </Link>
          )}
          <Link
            component="button"
            variant="body2"
            onClick={onToggleForm}
            sx={{ cursor: 'pointer' }}
            disabled={loading}
          >
            {t('noAccount')}
          </Link>
        </Box>
      </Box>
    </Paper>
  );
};

export default LoginForm;
