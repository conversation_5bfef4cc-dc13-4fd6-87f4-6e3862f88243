import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  Link,
  CircularProgress,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { authApi } from '../../services/api';
import { useLanguage } from '../../contexts/LanguageContext';

interface RegisterFormProps {
  onToggleForm: () => void;
  onClose: () => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onToggleForm, onClose }) => {
  // Form steps: 0=Email, 1=Password, 2=Verification, 3=Profile
  const [activeStep, setActiveStep] = useState(0);

  // Form fields
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [username, setUsername] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [country, setCountry] = useState('');

  // UI states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [codeSent, setCodeSent] = useState(false);

  const { t, language } = useLanguage();

  // List of countries for the dropdown
  const countries = [
    { value: 'CN', label: language === 'zh' ? '中国' : 'China' },
    { value: 'US', label: language === 'zh' ? '美国' : 'United States' },
    { value: 'JP', label: language === 'zh' ? '日本' : 'Japan' },
    { value: 'KR', label: language === 'zh' ? '韩国' : 'South Korea' },
    { value: 'DE', label: language === 'zh' ? '德国' : 'Germany' },
    { value: 'RU', label: language === 'zh' ? '俄罗斯' : 'Russia' },
    { value: 'GB', label: language === 'zh' ? '英国' : 'United Kingdom' },
    { value: 'FR', label: language === 'zh' ? '法国' : 'France' },
    { value: 'IN', label: language === 'zh' ? '印度' : 'India' },
    { value: 'BR', label: language === 'zh' ? '巴西' : 'Brazil' },
    { value: 'CA', label: language === 'zh' ? '加拿大' : 'Canada' },
    { value: 'AU', label: language === 'zh' ? '澳大利亚' : 'Australia' },
    { value: 'IT', label: language === 'zh' ? '意大利' : 'Italy' },
    { value: 'ES', label: language === 'zh' ? '西班牙' : 'Spain' },
    { value: 'MX', label: language === 'zh' ? '墨西哥' : 'Mexico' },
    { value: 'ID', label: language === 'zh' ? '印度尼西亚' : 'Indonesia' },
    { value: 'TR', label: language === 'zh' ? '土耳其' : 'Turkey' },
    { value: 'SA', label: language === 'zh' ? '沙特阿拉伯' : 'Saudi Arabia' },
    { value: 'ZA', label: language === 'zh' ? '南非' : 'South Africa' },
    { value: 'TH', label: language === 'zh' ? '泰国' : 'Thailand' },
    { value: 'SG', label: language === 'zh' ? '新加坡' : 'Singapore' },
    { value: 'MY', label: language === 'zh' ? '马来西亚' : 'Malaysia' },
    { value: 'VN', label: language === 'zh' ? '越南' : 'Vietnam' },
    { value: 'PH', label: language === 'zh' ? '菲律宾' : 'Philippines' },
    { value: 'AE', label: language === 'zh' ? '阿联酋' : 'United Arab Emirates' },
  ];

  const handleRequestVerification = async () => {
    if (!email) {
      setError(t('emailError'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await authApi.requestVerification(email);
      setCodeSent(true);
      setActiveStep(2); // Move to verification step

      // Always show the standard verification sent message
      setError(t('verificationSent'));
    } catch (err: any) {
      console.error('Verification request error:', err.response || err);
      setError(err.response?.data?.detail || t('verificationFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyEmail = async () => {
    if (!verificationCode) {
      setError(t('verificationFailed'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Store the verification response which contains the token and user info
      const verificationResponse = await authApi.verifyEmail({ email, code: verificationCode });

      // Store the token in localStorage
      if (verificationResponse.access_token) {
        localStorage.setItem('token', verificationResponse.access_token);
      }

      // Move to profile step
      setActiveStep(3);
      setError(t('verificationSuccess'));
    } catch (err: any) {
      console.error('Email verification error:', err.response || err);
      setError(err.response?.data?.detail || t('verificationFailed'));
    } finally {
      setLoading(false);
    }
  };

  const validatePassword = () => {
    if (password.length < 6) {
      setError(t('passwordHelp'));
      return false;
    }
    if (password !== confirmPassword) {
      setError(t('passwordsMustMatch'));
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate based on current step
    if (activeStep === 0) {
      if (!email) {
        setError(t('required'));
        return;
      }
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        setError(t('invalidEmail'));
        return;
      }
      // Move to password step
      setActiveStep(1);
      return;
    }

    if (activeStep === 1) {
      if (!password || !confirmPassword) {
        setError(t('required'));
        return;
      }
      if (!validatePassword()) {
        return;
      }
      // Request verification code
      handleRequestVerification();
      return;
    }

    if (activeStep === 2) {
      // Verify code
      handleVerifyEmail();
      return;
    }

    if (activeStep === 3) {
      if (!username || !country || !companyName) {
        setError(t('required'));
        return;
      }

      // Final registration - update user profile
      setLoading(true);
      setError('');

      try {
        // Check if we have a token (user is already verified)
        const token = localStorage.getItem('token');

        if (token) {
          // User is already verified, just update their profile
          await authApi.updateProfile({
            username,
            company_name: companyName,
            country,
            phone: phoneNumber
          });

          // Registration complete, redirect to main app
          window.location.href = '/'; // Or any other appropriate redirect
        } else {
          // Fallback to regular registration if no token (shouldn't normally happen)
          await authApi.register({
            username,
            email,
            password,
            company_name: companyName,
            country,
            phone: phoneNumber
          });
          onToggleForm(); // Switch to login form after successful registration
        }
      } catch (err: any) {
        console.error('Registration error:', err.response || err);
        const errorDetail = err.response?.data?.detail;
        if (typeof errorDetail === 'string') {
          setError(errorDetail);
        } else if (Array.isArray(errorDetail)) {
          setError(errorDetail[0]?.msg || t('registerFailed'));
        } else {
          setError(t('registerFailed'));
        }
      } finally {
        setLoading(false);
      }
    }
  };

  const getStepContent = (step: number) => {
    switch (step) {
      case 0: // Email step
        return (
          <TextField
            fullWidth
            label={t('email')}
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            margin="normal"
            required
            placeholder={t('email')}
            error={!!error && !email}
            disabled={loading}
            variant="outlined"
            sx={{ mt: 2, mb: 1 }}
            InputProps={{
              sx: { borderRadius: 1.5, fontSize: '1rem' }
            }}
          />
        );
      case 1: // Password step
        return (
          <>
            <TextField
              fullWidth
              label={t('password')}
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              margin="normal"
              required
              placeholder={t('password')}
              error={!!error && !password}
              disabled={loading}
              helperText={t('passwordHelp')}
              variant="outlined"
              sx={{ mt: 2, mb: 1 }}
              InputProps={{
                sx: { borderRadius: 1.5, fontSize: '1rem' }
              }}
            />
            <TextField
              fullWidth
              label={t('confirmPassword')}
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              margin="normal"
              required
              placeholder={t('confirmPassword')}
              error={!!error && (!confirmPassword || password !== confirmPassword)}
              disabled={loading}
              variant="outlined"
              sx={{ mt: 2, mb: 1 }}
              InputProps={{
                sx: { borderRadius: 1.5, fontSize: '1rem' }
              }}
            />
          </>
        );
      case 2: // Verification step
        return (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body1" gutterBottom sx={{ mb: 2, fontWeight: 'medium' }}>
              {t('verificationSent')}: <Box component="span" sx={{ fontWeight: 'bold' }}>{email}</Box>
            </Typography>
            <TextField
              fullWidth
              label={t('verificationCode')}
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              margin="normal"
              required
              placeholder={t('verificationCode')}
              error={!!error && !verificationCode}
              disabled={loading}
              variant="outlined"
              sx={{ mt: 1, mb: 1 }}
              InputProps={{
                sx: { borderRadius: 1.5, fontSize: '1rem' }
              }}
            />
          </Box>
        );
      case 3: // Profile step
        return (
          <>
            <TextField
              fullWidth
              label={t('username')}
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              margin="normal"
              required
              placeholder={t('username')}
              error={!!error && !username}
              disabled={loading}
              helperText={t('usernameHelp')}
              variant="outlined"
              sx={{ mt: 2, mb: 1 }}
              InputProps={{
                sx: { borderRadius: 1.5, fontSize: '1rem' }
              }}
            />

            <TextField
              fullWidth
              label={t('companyName')}
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              margin="normal"
              required
              placeholder={t('companyName')}
              error={!!error && !companyName}
              disabled={loading}
              helperText={t('companyNameHelp')}
              variant="outlined"
              sx={{ mt: 2, mb: 1 }}
              InputProps={{
                sx: { borderRadius: 1.5, fontSize: '1rem' }
              }}
            />

            <TextField
              fullWidth
              label={t('phoneNumber')}
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              margin="normal"
              placeholder={t('phoneNumber')}
              disabled={loading}
              helperText={t('optional')}
              variant="outlined"
              sx={{ mt: 2, mb: 1 }}
              InputProps={{
                sx: { borderRadius: 1.5, fontSize: '1rem' }
              }}
            />

            <FormControl
              fullWidth
              margin="normal"
              required
              error={!!error && !country}
              sx={{ mt: 2, mb: 1 }}
            >
              <InputLabel id="country-select-label">{t('country')}</InputLabel>
              <Select
                labelId="country-select-label"
                value={country}
                label={t('country')}
                onChange={(e) => setCountry(e.target.value)}
                disabled={loading}
                sx={{ borderRadius: 1.5, fontSize: '1rem' }}
                MenuProps={{
                  PaperProps: {
                    style: {
                      maxHeight: 300,
                    },
                  },
                }}
              >
                {countries.map((country) => (
                  <MenuItem key={country.value} value={country.value}>
                    {country.label}
                  </MenuItem>
                ))}
              </Select>
              {!!error && !country && <FormHelperText>{t('required')}</FormHelperText>}
            </FormControl>
          </>
        );
      default:
        return null;
    }
  };

  const getStepLabel = (step: number) => {
    switch(step) {
      case 0: return t('email');
      case 1: return t('password');
      case 2: return t('verification');
      case 3: return t('profile');
      default: return '';
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 450, mx: 'auto', mt: 4, position: 'relative', borderRadius: 2 }}>
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: 8,
          top: 8,
        }}
      >
        <CloseIcon />
      </IconButton>

      <Typography variant="h5" gutterBottom align="center" fontWeight="bold">
        {t('register')}
      </Typography>

      <Stepper activeStep={activeStep} sx={{ mt: 3, mb: 4 }}>
        {[0, 1, 2, 3].map((step) => (
          <Step key={step}>
            <StepLabel>{getStepLabel(step)}</StepLabel>
          </Step>
        ))}
      </Stepper>

      <Box component="form" noValidate onSubmit={handleSubmit} sx={{ mt: 2 }}>
        {getStepContent(activeStep)}

        {error && (
          <Box
            sx={{
              mt: 2,
              mb: 1,
              p: 1.5,
              borderRadius: 1.5,
              backgroundColor: error.includes(t('success')) ? 'success.light' : 'error.light',
              color: error.includes(t('success')) ? 'success.dark' : 'error.dark',
            }}
          >
            <Typography
              variant="body2"
              sx={{ fontWeight: 'medium' }}
            >
              {error}
            </Typography>
          </Box>
        )}

        <Button
          type="submit"
          fullWidth
          variant="contained"
          sx={{
            mt: 3,
            mb: 2,
            py: 1.2,
            fontSize: '1rem',
            fontWeight: 'bold',
            borderRadius: 1.5
          }}
          disabled={loading}
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : (
            activeStep === 3 ? t('completeRegistration') : t('nextStep')
          )}
        </Button>

        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <Link
            component="button"
            variant="body2"
            onClick={onToggleForm}
            sx={{ cursor: 'pointer', textDecoration: 'none' }}
            disabled={loading}
          >
            {t('hasAccount')}
          </Link>
        </Box>
      </Box>
    </Paper>
  );
};

export default RegisterForm;
