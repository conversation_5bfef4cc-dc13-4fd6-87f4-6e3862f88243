import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Typography,
  Paper,
  CircularProgress,
  Divider,
  Avatar,
  Fade,
  Card,
  Tooltip,
  Badge,
  useTheme,
  Alert,
  Chip,
  ToggleButtonGroup,
  ToggleButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
import ThumbUpOutlinedIcon from '@mui/icons-material/ThumbUpOutlined';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PersonIcon from '@mui/icons-material/Person';
import FeedbackIcon from '@mui/icons-material/Feedback';
import CalculateIcon from '@mui/icons-material/Calculate';
import TableChartIcon from '@mui/icons-material/TableChart';
import EmailIcon from '@mui/icons-material/Email';
import ChatIcon from '@mui/icons-material/Chat';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import InfoIcon from '@mui/icons-material/Info';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { v4 as uuidv4 } from 'uuid';
import { convertMarkdownTableToExcel, convertJsonTableToExcel } from '../../utils/excelUtils';
import { parseMarkdownTable, containsMarkdownTable, markdownTableToExcelHTML } from '../../utils/tableUtils';
import { useAuth } from '../../contexts/AuthContext';
import EmailDialog from './EmailDialog';
import { useLanguage } from '../../contexts/LanguageContext';
import { saveMessages, loadMessages } from '../../services/chatStorage';
import {
  Message as ChatMessage,
  Function as FunctionType,
  ConversionResult,
  TableData
} from '../../types/chat';
import {
  ChatSession
} from '../../types/chatSession';
import {
  getSession,
  getActiveSession,
  saveMessage,
  saveSessionIfNeeded,
  createSession,
  getOrCreateActiveSession
} from '../../services/chatSessionStorage';

// Function to get the icon component based on the icon name

// Function to get the icon component based on the icon name
const getIconForFunction = (iconName: string) => {
  switch(iconName) {
    case 'chat':
      return <ChatIcon />;
    case 'calculate':
      return <CalculateIcon />;
    case 'table_chart':
      return <TableChartIcon />;
    default:
      return <SmartToyIcon />;
  }
};

// Add a function to check if a message is a welcome message
const isWelcomeMessage = (message: ChatMessage): boolean => {
  return message.type === 'assistant' &&
    (message.content.includes('欢迎使用') ||
     message.content.includes('welcome') ||
     message.content.includes('Welcome'));
};

// Check if the current state is the initial welcome state (only welcome message)
const isInitialState = (messages: ChatMessage[]): boolean => {
  return messages.length === 1 && isWelcomeMessage(messages[0]);
};

const ConversionChat: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionMode, setConversionMode] = useState<'imperial-to-metric' | 'metric-to-imperial'>('imperial-to-metric');
  const [activeFunctions, setActiveFunctions] = useState<string[]>(['general']);
  const [functions, setFunctions] = useState<FunctionType[]>([]);
  const [liked, setLiked] = useState<number[]>([]);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [selectedResultData, setSelectedResultData] = useState<any>(null);
  const [emailResultType, setEmailResultType] = useState<'conversion' | 'table'>('conversion');
  const [isLoadingFunctions, setIsLoadingFunctions] = useState(true);
  const [activeSessionId, setActiveSessionId] = useState<string>('');
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [isSessionSwitching, setIsSessionSwitching] = useState(false);
  const [isInInitialState, setIsInInitialState] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { user } = useAuth();
  const theme = useTheme();
  const { t, language } = useLanguage();

  // Load available functions on mount
  useEffect(() => {
    const loadFunctions = async () => {
      try {
        setIsLoadingFunctions(true);
        // Import API_BASE_URL from config
        const { API_BASE_URL } = await import('../../config');
        console.log('Using API base URL:', API_BASE_URL);

        // Handle HTTPS fallback for production servers
        let apiUrl = `${API_BASE_URL}/llm/functions`;

        // Check if we're on HTTPS and need to fallback to HTTP
        if (window.location.protocol === 'https:' &&
            (window.location.hostname === 'steelnet.ai' ||
             window.location.hostname === '************')) {
          // Try to use the proxy first
          apiUrl = `/api/llm/functions`;
          console.log('Using proxy URL for HTTPS fallback:', apiUrl);
        }

        console.log('Fetching functions from:', apiUrl);
        const response = await fetch(apiUrl);
        console.log('Functions API response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('Functions loaded successfully:', data);
          setFunctions(data);
        } else {
          console.error('Failed to load functions, status:', response.status);
          // Set default functions if API fails
          setFunctions([
            {
              id: 'general',
              name: '钢铁行业咨询',
              description: '一般钢铁行业知识查询和咨询',
              icon: 'chat'
            },
            {
              id: 'conversion',
              name: '单位转换',
              description: '英制/公制单位转换',
              icon: 'calculate'
            },
            {
              id: 'table',
              name: '制表功能',
              description: '单位转换并生成表格',
              icon: 'table_chart'
            }
          ]);
        }
      } catch (error) {
        console.error('Error loading functions:', error);
        // Set default functions if API fails
        setFunctions([
          {
            id: 'general',
            name: '钢铁行业咨询',
            description: '一般钢铁行业知识查询和咨询',
            icon: 'chat'
          },
          {
            id: 'conversion',
            name: '单位转换',
            description: '英制/公制单位转换',
            icon: 'calculate'
          },
          {
            id: 'table',
            name: '制表功能',
            description: '单位转换并生成表格',
            icon: 'table_chart'
          }
        ]);
      } finally {
        setIsLoadingFunctions(false);
      }
    };

    loadFunctions();
  }, []);

  // Load and initialize active session on component mount
  useEffect(() => {
    const initializeActiveSession = async () => {
      try {
        setIsLoading(true);
        console.log('Initializing active session...');

        // Get or create an active session using the dedicated function
        const activeSession = await getOrCreateActiveSession();
        console.log('Retrieved active session:', activeSession);

        // Ensure messages is an array
        if (!Array.isArray(activeSession.messages)) {
          console.warn('Active session has invalid messages property, resetting to empty array');
          activeSession.messages = [];
        }

        // Update component state with session data
        setCurrentSession(activeSession);
        console.log('Setting messages state:', activeSession.messages);
        setMessages(activeSession.messages);
        setActiveSessionId(activeSession.id);

        // Check if this is the initial state (just welcome message)
        const isInitialWelcomeState = isInitialState(activeSession.messages);
        setIsInInitialState(isInitialWelcomeState);

        // Log for debugging
        console.log('Initialized active session:', activeSession.id,
                    'Initial state:', isInitialWelcomeState,
                    'Message count:', activeSession.messages.length);
      } catch (error) {
        console.error('Failed to initialize active session:', error);
        // As a fallback, display welcome message locally
        const welcomeMessage = {
          type: 'assistant' as const,
          content: t('welcome'),
          timestamp: new Date()
        };
        console.log('Setting fallback welcome message');
        setMessages([welcomeMessage]);
        setIsInInitialState(true);
      } finally {
        setIsLoading(false);
      }
    };

    initializeActiveSession();
  }, [t]); // Only run this once on mount (with t dependency for translations)

  // Save messages when they change
  useEffect(() => {
    if (messages.length > 0) {
      saveMessages(messages).catch(err =>
        console.error('Failed to save chat messages:', err)
      );
    }
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Focus input on load
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Process a user message and get AI response
  const processUserMessage = async (
    userMessage: string,
    sessionId: string,
    effectiveActiveFunctions: string[],
    userChatMessage: ChatMessage
  ) => {
    try {
      // Determine the unit direction based on the current mode
      const unitSystem = conversionMode === 'imperial-to-metric' ? 'metric' : 'imperial';

      // Import API_BASE_URL from config
      const { API_BASE_URL } = await import('../../config');

      // Determine if we need to use the conversion endpoint (legacy) or new LLM endpoint
      let endpoint = `${API_BASE_URL}/llm`;

      // Handle HTTPS fallback for production servers
      if (window.location.protocol === 'https:' &&
          (window.location.hostname === 'steelnet.ai' ||
           window.location.hostname === '************')) {
        // Try to use the proxy first
        endpoint = `/api/llm`;
        console.log('Using proxy URL for HTTPS fallback:', endpoint);
      }

      console.log('Sending message to API', {
        endpoint,
        text: userMessage,
        unit_system: unitSystem,
        function: effectiveActiveFunctions.join(',')
      });

      // For non-logged-in users, we don't include the Authorization header
      // The backend should still process the request
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(user && { Authorization: `Bearer ${localStorage.getItem('token')}` })
        },
        body: JSON.stringify({
          text: userMessage,
          unit_system: unitSystem,
          function: effectiveActiveFunctions.join(',')
        }),
      });

      console.log('API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API Error ${response.status}:`, errorText);
        throw new Error(`Error ${response.status}: ${errorText}`);
      }

      let data;
      try {
        data = await response.json();
        console.log('API response:', data);
      } catch (error) {
        console.error('Failed to parse API response as JSON:', error);
        throw new Error('Invalid response format from server');
      }

      if (!data || !data.result) {
        console.error('Invalid API response structure:', data);
        throw new Error('Invalid response structure from server');
      }

      let newAssistantMessage: ChatMessage;

      // For conversion and table functions
      if (effectiveActiveFunctions.includes('conversion') || effectiveActiveFunctions.includes('table')) {
        // Check if the response contains a markdown table
        const hasTable = data.result && data.result.converted_text &&
                        (data.result.hasTable || containsMarkdownTable(data.result.converted_text));

        newAssistantMessage = {
          type: 'assistant' as const,
          content: data.message || '以下是您的转换结果：',
          result: {
            ...data.result,
            hasTable: hasTable || effectiveActiveFunctions.includes('table')
          },
          function: effectiveActiveFunctions[0],
          timestamp: new Date()
        };
      } else {
        // For general queries
        newAssistantMessage = {
          type: 'assistant' as const,
          content: data.result?.text || '抱歉，无法处理您的请求。',
          function: effectiveActiveFunctions[0],
          timestamp: new Date()
        };
      }

      // Always add the assistant message to the UI for better user experience
      console.log('Adding assistant message to UI:', newAssistantMessage);
      setMessages(prev => {
        const updatedMessages = [...prev, newAssistantMessage];
        console.log('Updated messages state with assistant response:', updatedMessages);
        return updatedMessages;
      });

      // Try to save to the original submission session, but don't block if it fails
      try {
        await saveMessage(sessionId, newAssistantMessage);
        console.log(`Successfully saved assistant message to session ${sessionId}`);

        // Trigger a chat update event to refresh the sidebar
        // This ensures the chat appears in the sidebar with the latest messages
        window.dispatchEvent(new CustomEvent('chat-updated'));
      } catch (saveError) {
        console.warn(`Failed to save assistant message to session ${sessionId}, but chat flow continues`, saveError);
        // Show a brief non-blocking warning
        setError('助手回复未保存，但对话已完成。');
        // Clear error after 3 seconds
        setTimeout(() => setError(''), 3000);
      }
    } catch (error) {
      console.error('Processing error:', error);

      // Get more detailed error message if available
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      setError(`抱歉，处理过程中发生错误: ${errorMsg}。请稍后再试或联系支持团队。`);

      const errorMessage: ChatMessage = {
        type: 'assistant' as const,
        content: `处理失败: ${errorMsg}。请检查您的输入内容或网络连接。`,
        timestamp: new Date()
      };

      // Always show the error message in the UI for better user experience
      console.log('Adding error message to UI:', errorMessage);
      setMessages(prev => {
        const updatedMessages = [...prev, errorMessage];
        console.log('Updated messages state with error message:', updatedMessages);
        return updatedMessages;
      });

      // Save the error to the original session
      await saveMessage(sessionId, errorMessage);
    } finally {
      setIsLoading(false);

      // Only focus back on input if we're still on the same session
      try {
        const currentActiveSessionId = await getActiveSession();
        if (sessionId === currentActiveSessionId) {
          setTimeout(() => inputRef.current?.focus(), 100);
        }
      } catch (error) {
        console.error('Failed to get active session:', error);
        // Focus back on input anyway
        setTimeout(() => inputRef.current?.focus(), 100);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage = input.trim();
    setInput('');
    setError(null);

    // Determine the active function - if none selected, use 'general'
    const effectiveActiveFunctions = activeFunctions.length === 0 ? ['general'] : activeFunctions;

    const newUserMessage: ChatMessage = {
      type: 'user',
      content: userMessage,
      timestamp: new Date(),
      function: effectiveActiveFunctions[0]
    };

    // Capture the session ID at the time of submission to ensure the response
    // is added to the correct session, even if the user switches later
    let submissionSessionId = activeSessionId;

    // If no active session, create one immediately without blocking the user
    if (!submissionSessionId) {
      console.log('No active session found. Creating a new session on-the-fly.');

      // Create a temporary session ID for immediate use
      const tempSessionId = uuidv4();

      // Create a temporary session in memory
      const tempSession: ChatSession = {
        id: tempSessionId,
        title: userMessage.substring(0, 20) + '...',
        lastUpdated: new Date(),
        messages: [{
          type: 'assistant',
          content: '欢迎使用钢铁单位转换器!',
          timestamp: new Date()
        }, newUserMessage],
        function: effectiveActiveFunctions[0],
        preview: userMessage.substring(0, 50)
      };

      // Update UI immediately
      setActiveSessionId(tempSessionId);
      setCurrentSession(tempSession);
      setMessages(tempSession.messages);
      setIsInInitialState(false);

      // Add to cache
      sessionCache[tempSessionId] = tempSession;

      // Start creating the real session in the background
      createSession(newUserMessage)
        .then(newSession => {
          console.log('Created permanent session with ID:', newSession.id);
          // Update with the real session ID once it's created
          setActiveSessionId(newSession.id);
          setCurrentSession(newSession);
          // No need to update messages as they should be the same
        })
        .catch(error => {
          console.error('Failed to create a permanent session:', error);
          // Continue with the temporary session - don't block the user
        });

      // Continue with the temporary session ID
      submissionSessionId = tempSessionId;
    }

    // Add message to UI right away
    console.log('Adding user message to UI:', newUserMessage);
    setMessages(prev => {
      const updatedMessages = [...prev, newUserMessage];
      console.log('Updated messages state:', updatedMessages);
      return updatedMessages;
    });

    // If this is the first user message, the chat is no longer in initial state
    if (isInInitialState) {
      setIsInInitialState(false);
      console.log('Chat transitioned from initial state to history');

      // Trigger a chat update event to refresh the sidebar
      // This ensures the chat appears in the sidebar as soon as it's no longer in initial state
      window.dispatchEvent(new CustomEvent('chat-updated'));
    }

    // Try to save message to session storage, but continue flow even if it fails
    try {
      console.log('=== CONVERSION CHAT: SAVE MESSAGE START ===');
      console.log(`Saving user message to session ${submissionSessionId}`);
      console.log('Message type:', newUserMessage.type);
      console.log('Message content:', newUserMessage.content);

      // Explicitly trigger a chat update event BEFORE saving
      // This ensures the sidebar is refreshed even if saving fails
      console.log('Dispatching chat-updated event BEFORE saving message');
      window.dispatchEvent(new CustomEvent('chat-updated'));

      const updatedSession = await saveMessage(submissionSessionId, newUserMessage);

      if (!updatedSession) {
        console.warn(`Failed to save message to session ${submissionSessionId}, but continuing chat flow`);
        // Show a non-blocking warning instead of an error that stops the flow
        setError('消息未保存，但对话将继续。');
      } else {
        console.log(`Successfully saved message to session ${submissionSessionId}`);

        // Explicitly trigger a chat update event AFTER saving
        // This ensures the chat appears in the sidebar immediately
        console.log('Dispatching chat-updated event AFTER saving message');
        window.dispatchEvent(new CustomEvent('chat-updated'));
      }

      console.log('=== CONVERSION CHAT: SAVE MESSAGE END ===');
    } catch (error) {
      console.warn('Error saving message, but continuing chat flow:', error);
      // Show a non-blocking warning
      setError('消息保存失败，但对话将继续。');

      // Even if saving fails, still try to update the sidebar
      console.log('Dispatching chat-updated event despite save error');
      window.dispatchEvent(new CustomEvent('chat-updated'));
    }

    // Clear error after 3 seconds
    setTimeout(() => setError(''), 3000);

    setIsLoading(true);

    // Process the user message and get AI response
    await processUserMessage(userMessage, submissionSessionId, effectiveActiveFunctions, newUserMessage);
  };

  const calculateWeight = (value: number, unit: string): number => {
    // Simple weight calculation for example purposes
    // In a real app, this would use proper steel density calculations
    return value * 7.85 * 0.001; // Simple approximation for steel in kg
  };

  const handleCopy = (index: number, text: string, format: 'text' | 'html' = 'text') => {
    if (format === 'html') {
      // Create a temporary element to hold the HTML content
      const tempElement = document.createElement('div');
      tempElement.innerHTML = text;

      // Select the element
      document.body.appendChild(tempElement);
      const range = document.createRange();
      range.selectNode(tempElement);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
        document.execCommand('copy');
        selection.removeAllRanges();
      }
      document.body.removeChild(tempElement);
    } else {
      // Regular text copy
      navigator.clipboard.writeText(text);
    }

    setCopied(index);
    setTimeout(() => setCopied(null), 2000);
  };

  // Copy table in Excel-friendly format
  const handleCopyTableForExcel = (index: number, markdownTable: string) => {
    try {
      const htmlTable = markdownTableToExcelHTML(markdownTable);
      handleCopy(index, htmlTable, 'html');
    } catch (error) {
      console.error('Error copying table for Excel:', error);
      // Fallback to regular text copy
      handleCopy(index, markdownTable);
    }
  };

  const handleLike = (index: number) => {
    if (liked.includes(index)) {
      setLiked(liked.filter(i => i !== index));
    } else {
      setLiked([...liked, index]);
    }
  };

  const handleFeedback = (index: number) => {
    // Implement feedback functionality
    alert(t('feedbackThanks'));
  };

  const handleReportError = (index: number) => {
    // Implement error reporting
    alert(t('errorReportThanks'));
  };

  const handleConversionModeChange = (_: React.MouseEvent<HTMLElement>, newMode: 'imperial-to-metric' | 'metric-to-imperial' | null) => {
    if (newMode !== null) {
      setConversionMode(newMode);
    }
  };

  const handleEmailResult = (result: ConversionResult | TableData, type: 'conversion' | 'table' = 'conversion') => {
    // Set the selected result data and open the email dialog
    setSelectedResultData(result);
    setEmailResultType(type);
    setEmailDialogOpen(true);
  };

  // Handle Excel download for tables
  const handleExcelDownload = (data: any, type: 'markdown' | 'json' = 'markdown') => {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `conversion_table_${timestamp}.xlsx`;

      if (type === 'markdown') {
        // For markdown tables (from message.result.converted_text)
        convertMarkdownTableToExcel(data, fileName);
      } else {
        // For JSON data (from message.tableData)
        convertJsonTableToExcel(data, fileName);
      }
    } catch (error) {
      console.error('Error downloading Excel file:', error);
      setError('Excel文件下载失败，请稍后再试。');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get the active function name
  const getActiveFunctionName = () => {
    // If no functions are selected or only general is selected, return the general function name
    if (activeFunctions.length === 0 || (activeFunctions.length === 1 && activeFunctions[0] === 'general')) {
      const generalFunc = functions.find(f => f.id === 'general');
      return generalFunc ? generalFunc.name : '钢铁助手';
    }

    // Otherwise return the name of the first selected function
    const activeFunc = functions.find(f => activeFunctions.includes(f.id));
    return activeFunc ? activeFunc.name : '钢铁助手';
  };

  // Get the active function icon
  const getActiveFunctionIcon = () => {
    // If no functions are selected or only general is selected, return the general function icon
    if (activeFunctions.length === 0 || (activeFunctions.length === 1 && activeFunctions[0] === 'general')) {
      const generalFunc = functions.find(f => f.id === 'general');
      return generalFunc ? getIconForFunction(generalFunc.icon) : <SmartToyIcon />;
    }

    // Otherwise return the icon of the first selected function
    const activeFunc = functions.find(f => activeFunctions.includes(f.id));
    return activeFunc ? getIconForFunction(activeFunc.icon) : <SmartToyIcon />;
  };

  const toggleFunction = (id: string) => {
    // Don't allow toggling "general" function as it's the default
    if (id === 'general') return;

    if (activeFunctions.includes(id)) {
      // Remove the function if it's active
      setActiveFunctions(activeFunctions.filter(f => f !== id));
    } else {
      // Add the function while keeping others
      setActiveFunctions([...activeFunctions.filter(f => f !== 'general'), id]);
    }
  };

  // Monitor active session changes
  useEffect(() => {
    const checkActiveSession = async () => {
      try {
        const currentActiveSessionId = await getActiveSession();

        // Only reload if the active session has changed
        if (currentActiveSessionId && currentActiveSessionId !== activeSessionId) {
          setIsSessionSwitching(true);
          try {
            // Always fetch fresh data from storage/API
            console.log("Fetching fresh session data from storage:", currentActiveSessionId);
            const freshSession = await getSession(currentActiveSessionId);

            if (freshSession) {
              // Update with the fresh data
              setCurrentSession(freshSession);
              setMessages(freshSession.messages);
              setActiveSessionId(currentActiveSessionId);
            }
            setIsSessionSwitching(false);
          } catch (error) {
            console.error('Failed to load active session:', error);
            setIsSessionSwitching(false);
          }
        }
      } catch (error) {
        console.error('Failed to get active session:', error);
      }
    };

    // Check active session changes more frequently to ensure we quickly
    // detect changes and update the UI accordingly
    const intervalId = setInterval(checkActiveSession, 300);

    // Initial check
    checkActiveSession();

    return () => clearInterval(intervalId);
  }, [activeSessionId]);

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      position: 'relative',
      backgroundColor: '#F9FAFB', // Light gray background similar to ChatGPT
    }}>
      {/* Error alert */}
      {error && (
        <Fade in={!!error}>
          <Alert
            severity="error"
            sx={{ m: 2 }}
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        </Fade>
      )}

      {/* Chat messages */}
      <Box
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
          p: { xs: 2, md: 3 },
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
        }}
      >
        {/* Don't render duplicate welcome messages */}
        {messages.map((message, index) => {
          // If this is a welcome message and not the first one, don't show it
          const isWelcome = isWelcomeMessage(message);
          const isFirst = index === 0;
          const showMessage = !isWelcome || isFirst || message.type === 'user';

          if (!showMessage) return null;

          return (
            <Fade key={index} in={true} timeout={300} style={{ transitionDelay: `${50 * index}ms` }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: message.type === 'user' ? 'row-reverse' : 'row',
                  alignItems: 'flex-start',
                  maxWidth: '1200px',
                  mx: 'auto',
                  width: '100%',
                }}
              >
                {/* Avatar for assistant */}
                {message.type === 'assistant' && (
                <Avatar
                  sx={{
                      bgcolor: '#10A37F', // ChatGPT green
                      alignSelf: 'flex-start',
                      mr: 2,
                      width: 30,
                      height: 30,
                    }}
                  >
                    <SmartToyIcon sx={{ fontSize: 18 }} />
                </Avatar>
                )}

                {/* Avatar for user */}
                {message.type === 'user' && (
                <Avatar
                  sx={{
                      bgcolor: theme.palette.primary.main,
                      alignSelf: 'flex-start',
                      ml: 2,
                      width: 30,
                      height: 30,
                    }}
                  >
                    <PersonIcon sx={{ fontSize: 18 }} />
                </Avatar>
                )}

                {/* Message content */}
                <Box
                  sx={{
                    flexGrow: 0,
                    maxWidth: message.type === 'user' ? '70%' : '90%', // Wider for assistant messages with diagrams
                    backgroundColor: message.type === 'user' ? theme.palette.primary.main : 'white',
                    color: message.type === 'user' ? 'white' : 'inherit',
                    p: 2,
                    borderRadius: message.type === 'user'
                      ? '1rem 0 1rem 1rem' // User message bubble shape
                      : '0 1rem 1rem 1rem', // Assistant message bubble shape
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                    position: 'relative',
                  }}
                >
                  {/* Show function type for user messages */}
                  {message.type === 'user' && message.function && (
                    <Chip
                      size="small"
                      label={functions.find(f => f.id === message.function)?.name || message.function}
                      icon={getIconForFunction(functions.find(f => f.id === message.function)?.icon || 'chat')}
                      sx={{
                        mb: 1,
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                        color: 'primary.main',
                        fontWeight: 'medium',
                        '& .MuiChip-icon': { color: 'primary.main' }
                      }}
                    />
                  )}

                  <Typography
                    variant="body1"
                    sx={{
                      whiteSpace: 'pre-wrap',
                      color: message.type === 'user' ? 'white' : 'text.primary',
                      fontWeight: message.type === 'user' ? 400 : 400,
                      lineHeight: 1.6,
                      wordBreak: 'break-word',
                      width: '100%',
                      '& pre': {
                        overflowX: 'auto',
                        backgroundColor: 'rgba(0, 0, 0, 0.05)',
                        padding: '0.75rem',
                        borderRadius: '0.5rem',
                        fontFamily: 'monospace',
                        fontSize: '0.9rem',
                        maxWidth: '100%'
                      },
                      '& code': {
                        fontFamily: 'monospace',
                        backgroundColor: 'rgba(0, 0, 0, 0.05)',
                        padding: '0.2rem 0.4rem',
                        borderRadius: '0.25rem',
                        fontSize: '0.9rem'
                      }
                    }}
                  >
                    {message.content}
                  </Typography>

                  {/* Table View for legacy mode */}
                  {message.isTable && message.tableData && (
                    <Box sx={{ mt: 2 }}>
                      <Divider sx={{ my: 1 }} />
                      <TableContainer component={Paper} sx={{ boxShadow: 'none', border: '1px solid rgba(0,0,0,0.1)', overflowX: 'auto', width: '100%' }}>
                        <Table size="small" sx={{ minWidth: 650, tableLayout: 'auto' }}>
                          <TableHead>
                            <TableRow>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('steelType')}</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('length')} ({message.tableData?.length?.unit || 'mm'})</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('width')} ({message.tableData?.width?.unit || 'mm'})</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('thickness')} ({message.tableData?.thickness?.unit || 'mm'})</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('weight')} ({message.tableData?.weight?.unit})</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData.steelType}</TableCell>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData?.length?.value.toFixed(2) || '-'}</TableCell>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData?.width?.value.toFixed(2) || '-'}</TableCell>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData?.thickness?.value.toFixed(2) || '-'}</TableCell>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData?.weight?.value.toFixed(2) || '-'}</TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>

                      {/* Table result actions */}
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1, gap: 1 }}>
                        <Tooltip title={t('copy')}>
                          <IconButton
                            size="small"
                            onClick={() => handleCopy(index, JSON.stringify(message.tableData))}
                            color={copied === index ? 'success' : 'default'}
                          >
                            {copied === index ? <CheckIcon /> : <ContentCopyIcon />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('copyForExcel') || "Copy for Excel"}>
                          <IconButton
                            size="small"
                            onClick={() => {
                              // Convert tableData to markdown format first
                              const headers = ['Steel Type', 'Length', 'Width', 'Thickness', 'Weight'];
                              const row = [
                                message.tableData.steelType || '',
                                `${message.tableData?.length?.value.toFixed(2) || '-'} ${message.tableData?.length?.unit || 'mm'}`,
                                `${message.tableData?.width?.value.toFixed(2) || '-'} ${message.tableData?.width?.unit || 'mm'}`,
                                `${message.tableData?.thickness?.value.toFixed(2) || '-'} ${message.tableData?.thickness?.unit || 'mm'}`,
                                `${message.tableData?.weight?.value.toFixed(2) || '-'} ${message.tableData?.weight?.unit || 'kg'}`
                              ];

                              // Create a simple HTML table
                              const htmlTable = `<table>
                                <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
                                <tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>
                              </table>`;

                              handleCopy(index, htmlTable, 'html');
                            }}
                            color={copied === index ? 'success' : 'default'}
                          >
                            {copied === index ? <CheckIcon /> : <TableChartIcon />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('like')}>
                          <IconButton
                            size="small"
                            onClick={() => handleLike(index)}
                            color={liked.includes(index) ? 'primary' : 'default'}
                          >
                            {liked.includes(index) ? <ThumbUpAltIcon /> : <ThumbUpOutlinedIcon />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('reportError')}>
                          <IconButton
                            size="small"
                            onClick={() => handleReportError(index)}
                          >
                            <ErrorOutlineIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('feedback')}>
                          <IconButton
                            size="small"
                            onClick={() => handleFeedback(index)}
                          >
                            <FeedbackIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('downloadExcel')}>
                          <IconButton
                            size="small"
                            onClick={() => handleExcelDownload(message.tableData, 'json')}
                            color="primary"
                          >
                            <FileDownloadIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('sendToEmail')}>
                          <IconButton
                            size="small"
                            onClick={() => handleEmailResult(message.tableData!, 'table')}
                          >
                            <EmailIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                  )}

                  {/* Simple Conversion Result */}
                  {message.result && !message.isTable && (
                      <Box sx={{ mt: 2 }}>
                        <Divider sx={{ my: 1 }} />

                        {/* If the result has a table, render it as HTML */}
                        {message.result.hasTable ? (
                          <Box>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                              Conversion Table:
                            </Typography>

                            {(() => {
                              const { headers, rows } = parseMarkdownTable(message.result.converted_text);
                              return (
                                <>
                                  <TableContainer component={Paper} sx={{ boxShadow: 'none', border: '1px solid rgba(0,0,0,0.1)', mb: 2, overflowX: 'auto', width: '100%' }}>
                                    <Table size="small" sx={{ minWidth: 650, tableLayout: 'auto' }}>
                                      <TableHead>
                                        <TableRow>
                                          {headers.map((header, idx) => (
                                            <TableCell key={idx} sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{header}</TableCell>
                                          ))}
                                        </TableRow>
                                      </TableHead>
                                      <TableBody>
                                        {rows.map((row, rowIdx) => (
                                          <TableRow key={rowIdx}>
                                            {headers.map((header, cellIdx) => (
                                              <TableCell key={`${rowIdx}-${cellIdx}`} sx={{ wordBreak: 'break-word' }}>{row[header]}</TableCell>
                                            ))}
                                          </TableRow>
                                        ))}
                                      </TableBody>
                                    </Table>
                                  </TableContainer>
                                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 1, gap: 1 }}>
                                    <Tooltip title={t('copyForExcel') || "Copy for Excel"}>
                                      <Button
                                        size="small"
                                        startIcon={<ContentCopyIcon />}
                                        onClick={() => handleCopyTableForExcel(index, message.result.converted_text)}
                                        variant="outlined"
                                        color={copied === index ? 'success' : 'primary'}
                                        sx={{ fontSize: '0.75rem' }}
                                      >
                                        {copied === index ? (t('copied') || "Copied!") : (t('copyForExcel') || "Copy for Excel")}
                                      </Button>
                                    </Tooltip>
                                    <Tooltip title={t('downloadExcel')}>
                                      <Button
                                        size="small"
                                        startIcon={<FileDownloadIcon />}
                                        onClick={() => handleExcelDownload(message.result.converted_text, 'markdown')}
                                        variant="outlined"
                                        color="primary"
                                        sx={{ fontSize: '0.75rem' }}
                                      >
                                        {t('downloadExcel')}
                                      </Button>
                                    </Tooltip>
                                  </Box>
                                </>
                              );
                            })()}
                          </Box>
                        ) : (
                          // Regular text conversion display
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
                            <Typography variant="subtitle2" sx={{
                              fontWeight: 'bold',
                              color: message.type === 'user' ? 'rgba(255,255,255,0.9)' : 'text.primary'
                            }}>
                              Original:
                            </Typography>
                            <Typography
                              variant="body2"
                              component="div"
                              sx={{
                                p: 1.5,
                                bgcolor: message.type === 'user' ? 'rgba(255,255,255,0.15)' : '#f5f5f5',
                                borderRadius: 1,
                                fontFamily: 'monospace',
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-word',
                                color: message.type === 'user' ? 'rgba(255,255,255,0.9)' : 'text.primary'
                              }}
                            >
                              {message.result.original_text}
                            </Typography>

                            <Typography variant="subtitle2" sx={{
                              fontWeight: 'bold',
                              mt: 1,
                              color: message.type === 'user' ? 'rgba(255,255,255,0.9)' : 'text.primary'
                            }}>
                              Converted:
                            </Typography>
                            <Typography
                              variant="body2"
                              component="div"
                              sx={{
                                p: 1.5,
                                bgcolor: message.type === 'user' ? 'rgba(255,255,255,0.25)' : '#e3f2fd',
                                borderRadius: 1,
                                fontFamily: 'monospace',
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-word',
                                fontWeight: 'medium',
                                color: message.type === 'user' ? 'white' : 'text.primary'
                              }}
                            >
                              {message.result.converted_text}
                            </Typography>
                          </Box>
                        )}

                      {/* Conversion result actions */}
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        mt: 1,
                        gap: 1,
                        '& .MuiIconButton-root': {
                          color: message.type === 'user' ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.6)',
                          '&:hover': {
                            color: message.type === 'user' ? 'white' : 'rgba(0,0,0,0.8)',
                            backgroundColor: message.type === 'user' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                          }
                        }
                      }}>
                        <Tooltip title={t('copy')}>
                          <IconButton
                            size="small"
                            onClick={() => handleCopy(index, message.result?.converted_text || '')}
                            color={copied === index ? 'success' : 'default'}
                          >
                            {copied === index ? <CheckIcon fontSize="small" /> : <ContentCopyIcon fontSize="small" />}
                          </IconButton>
                        </Tooltip>
                        {message.result.hasTable && (
                          <Tooltip title={t('copyForExcel') || "Copy for Excel"}>
                            <IconButton
                              size="small"
                              onClick={() => handleCopyTableForExcel(index, message.result.converted_text)}
                              color={copied === index ? 'success' : 'default'}
                            >
                              {copied === index ? <CheckIcon fontSize="small" /> : <TableChartIcon fontSize="small" />}
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title={t('like')}>
                          <IconButton
                            size="small"
                            onClick={() => handleLike(index)}
                            color={liked.includes(index) ? 'primary' : 'default'}
                          >
                            {liked.includes(index) ? <ThumbUpAltIcon fontSize="small" /> : <ThumbUpOutlinedIcon fontSize="small" />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('reportError')}>
                          <IconButton
                            size="small"
                            onClick={() => handleReportError(index)}
                          >
                            <ErrorOutlineIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('feedback')}>
                          <IconButton
                            size="small"
                            onClick={() => handleFeedback(index)}
                          >
                            <FeedbackIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        {message.result.hasTable && (
                          <Tooltip title={t('downloadExcel')}>
                            <IconButton
                              size="small"
                              onClick={() => handleExcelDownload(message.result.converted_text, 'markdown')}
                              color="primary"
                            >
                              <FileDownloadIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title={t('sendToEmail')}>
                          <IconButton
                            size="small"
                            onClick={() => handleEmailResult(message.result!, 'conversion')}
                          >
                            <EmailIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                      </Box>
                    )}

                  {/* Message timestamp */}
                  <Box sx={{
                    display: 'flex',
                    justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                    alignItems: 'center',
                    mt: 1,
                    opacity: 0.7
                  }}>
                    <Typography variant="caption" sx={{ fontSize: '0.7rem', color: message.type === 'user' ? 'text.secondary' : 'text.secondary' }}>
                      {formatTime(message.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Fade>
          );
        })}
        <div ref={messagesEndRef} />
      </Box>

      {/* Loading indicator */}
      {isLoading && (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          p: 2,
          position: 'relative'
        }}>
          <CircularProgress size={30} thickness={4} sx={{ color: 'primary.main' }} />
        </Box>
      )}

      {/* Input form - ChatGPT style */}
      <Box
        sx={{
          p: 0,
          position: 'relative',
          width: '100%'
        }}
      >
        <Box
          component="form"
          onSubmit={handleSubmit}
          sx={{
            p: 2,
            maxWidth: '1200px',
            mx: 'auto',
            width: '100%',
          }}
        >
          {isInInitialState && (
            <Box sx={{ mb: 2, textAlign: 'center' }}>
              <Chip
                icon={<InfoIcon fontSize="small" />}
                label="初始欢迎状态 - 发送消息开始新对话"
                color="primary"
                variant="outlined"
                sx={{ mb: 1 }}
              />
            </Box>
          )}

          <Paper
            elevation={0}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              border: '1px solid',
              borderColor: isInInitialState ? 'primary.main' : 'divider',
              borderRadius: '0.75rem',
              '&:hover': {
                borderColor: 'primary.main',
              },
            }}
          >
            {/* Message input area */}
            <Box sx={{
              display: 'flex',
              p: 1.5,
              minHeight: '56px',
              alignItems: 'flex-end'
            }}>
              <TextField
                fullWidth
                multiline
                placeholder={t('inputPlaceholder')}
                variant="standard"
                value={input}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInput(e.target.value)}
                inputRef={inputRef}
                InputProps={{
                  disableUnderline: true,
                  sx: {
                    fontSize: '1rem',
                    lineHeight: 1.5,
                  }
                }}
                maxRows={8}
                sx={{ flexGrow: 1 }}
              />

              <IconButton
                color="primary"
                type="submit"
                disabled={isLoading || !input.trim()}
                sx={{
                  bgcolor: input.trim() ? 'primary.main' : 'transparent',
                  color: input.trim() ? 'white' : 'text.disabled',
                  border: input.trim() ? 'none' : '1px solid',
                  borderColor: 'divider',
                  ml: 1,
                  '&:hover': {
                    bgcolor: input.trim() ? 'primary.dark' : 'rgba(0,0,0,0.04)',
                  },
                  '&.Mui-disabled': {
                    bgcolor: 'action.disabledBackground',
                    opacity: 0.5,
                  },
                  transition: 'all 0.3s',
                  width: 40,
                  height: 40,
                }}
              >
                <SendIcon />
              </IconButton>
            </Box>

            {/* Function buttons - bottom aligned like ChatGPT with more padding */}
            <Box sx={{
              borderTop: '1px solid',
              borderColor: 'divider',
              py: 1.5,
              px: 2.5,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <Box sx={{ display: 'flex', gap: 1.5 }}>
                {!isLoadingFunctions ? (
                  functions
                    .filter(func => func.id !== 'general') // Don't show the 'general' function button
                    .map((func) => (
                      <Chip
                        key={func.id}
                        icon={getIconForFunction(func.icon)}
                        label={func.name}
                        variant={activeFunctions.includes(func.id) ? "filled" : "outlined"}
                        onClick={() => toggleFunction(func.id)}
                        color={activeFunctions.includes(func.id) ? "primary" : "default"}
                        size="small"
                        sx={{
                          borderRadius: '1rem',
                          height: '30px',
                          fontSize: '0.75rem',
                          px: 0.75,
                          cursor: 'pointer',
                          '&:hover': {
                            borderColor: 'primary.main',
                          }
                        }}
                      />
                    ))
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CircularProgress size={16} />
                    <Typography variant="caption">加载功能中...</Typography>
                  </Box>
                )}
              </Box>

              {activeFunctions.includes('conversion') && (
                <ToggleButtonGroup
                  value={conversionMode}
                  exclusive
                  onChange={handleConversionModeChange}
                  aria-label="conversion mode"
                  size="small"
                  sx={{ height: '30px', ml: 2 }}
                >
                  <ToggleButton value="imperial-to-metric" sx={{ px: 1.5, fontSize: '0.75rem' }}>
                    {t('imperialToMetric')}
                  </ToggleButton>
                  <ToggleButton value="metric-to-imperial" sx={{ px: 1.5, fontSize: '0.75rem' }}>
                    {t('metricToImperial')}
                  </ToggleButton>
                </ToggleButtonGroup>
              )}
            </Box>
          </Paper>

          <Typography
            variant="caption"
            align="center"
            sx={{
              display: 'block',
              mt: 1,
              color: 'text.secondary',
              opacity: 0.7
            }}
          >
            {t('appName')} - {t('tagline')}
          </Typography>
        </Box>
      </Box>

      {/* Email Dialog */}
      {emailDialogOpen && selectedResultData && (
        <EmailDialog
          open={emailDialogOpen}
          onClose={() => setEmailDialogOpen(false)}
          resultData={selectedResultData}
          resultType={emailResultType}
        />
      )}
    </Box>
  );
};

export default ConversionChat;
