import React, { useState } from 'react';
import {
  Box,
  Container,
  Icon<PERSON><PERSON>on,
  Drawer,
  useMediaQuery,
  AppBar,
  Toolbar,
  Typography,
  useTheme
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import LanguageSwitcher from '../LanguageSwitcher';
import { useLanguage } from '../../contexts/LanguageContext';

interface ChatLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
}

const DRAWER_WIDTH = 260;

const ChatLayout: React.FC<ChatLayoutProps> = ({ children, sidebar }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const { t } = useLanguage();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: '#F9FAFB' }}>
      {/* App Bar - only visible on mobile */}
      {isMobile && (
        <AppBar
          position="fixed"
          sx={{
            width: '100%',
            zIndex: theme.zIndex.drawer + 1,
            backgroundColor: 'white',
            color: '#202123',
            boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
          }}
          elevation={0}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', fontWeight: 600 }}>
              <SmartToyIcon sx={{ mr: 1, color: '#10A37F' }} /> {t('appName')}
            </Typography>
            <LanguageSwitcher />
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar - permanent on desktop, drawer on mobile */}
      {sidebar && (
        <>
          {/* Desktop sidebar */}
          <Box
            component="nav"
            sx={{
              width: DRAWER_WIDTH,
              flexShrink: 0,
              display: { xs: 'none', md: 'block' },
              boxShadow: '0 0 15px rgba(0,0,0,0.1)',
              bgcolor: theme.palette.customGradient.main,
              background: theme.palette.customGradient.gradient,
              color: '#FFFFFF',
              position: 'fixed',
              overflowX: 'hidden',
              overflowY: 'hidden',
              height: '100vh',
              zIndex: 1
            }}
          >
            <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                <SmartToyIcon sx={{ mr: 1, color: '#10A37F' }} /> {t('appName')}
              </Typography>
            </Box>
            <Box sx={{
              height: 'calc(100vh - 64px)',
              position: 'relative',
              display: 'flex',
              flexDirection: 'column',
            }}>
              {sidebar}
            </Box>
          </Box>

          {/* Mobile drawer */}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{ keepMounted: true }}
            sx={{
              display: { xs: 'block', md: 'none' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: DRAWER_WIDTH,
                bgcolor: theme.palette.customGradient.main,
                background: theme.palette.customGradient.gradient,
                color: '#FFFFFF',
                overflowY: 'hidden',
              },
            }}
          >
            <Box sx={{ p: 1, display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
              <IconButton onClick={handleDrawerToggle} sx={{ color: 'white' }}>
                <ChevronLeftIcon />
              </IconButton>
            </Box>
            <Box sx={{
              height: 'calc(100% - 48px)',
              position: 'relative',
              display: 'flex',
              flexDirection: 'column',
            }}>
              {sidebar}
            </Box>
          </Drawer>
        </>
      )}

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          pt: isMobile ? 8 : 0,
          ml: { xs: 0, md: `${DRAWER_WIDTH}px` },
          transition: theme.transitions.create(['margin', 'width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        {!isMobile && (
          <LanguageSwitcher fixed={true} />
        )}
        <Container
          maxWidth="xl"
          sx={{
            flexGrow: 1,
            py: { xs: 2, md: 3 },
            px: { xs: 0, sm: 2 },
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Box
            sx={{
              flexGrow: 1,
              bgcolor: '#F9FAFB',
              borderRadius: 0,
              overflow: 'hidden',
              height: '100%',
            }}
          >
            {children}
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default ChatLayout;
