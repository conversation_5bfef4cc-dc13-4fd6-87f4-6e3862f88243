/**
 * Utility functions for table operations
 */

/**
 * Parse a markdown table into headers and rows
 * @param markdownTable The markdown table content
 * @returns An object with headers and rows
 */
export interface ParsedTableRow {
  [key: string]: string;
}

export interface ParsedTable {
  headers: string[];
  rows: ParsedTableRow[];
}

/**
 * Parse a markdown table into headers and rows
 * @param markdownTable The markdown table content
 * @returns An object with headers and rows
 */
export const parseMarkdownTable = (markdownTable: string): ParsedTable => {
  const lines = markdownTable.trim().split('\n');

  if (lines.length < 3) {
    return { headers: [], rows: [] };
  }

  // Parse headers - remove leading/trailing | and split by |
  const headerLine = lines[0].trim();
  const headers = headerLine.startsWith('|') && headerLine.endsWith('|')
    ? headerLine.slice(1, -1).split('|').map(h => h.trim())
    : headerLine.split('|').map(h => h.trim());

  // Skip the separator line (line 1)

  // Parse rows
  const rows: ParsedTableRow[] = [];
  for (let i = 2; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line || line.startsWith('<!--')) continue;

    const cells = line.startsWith('|') && line.endsWith('|')
      ? line.slice(1, -1).split('|').map(cell => cell.trim())
      : line.split('|').map(cell => cell.trim());

    const row: ParsedTableRow = {};
    headers.forEach((header, index) => {
      row[header] = cells[index] || '';
    });

    rows.push(row);
  }

  return { headers, rows };
};

/**
 * Convert a parsed table to HTML format suitable for copying to Excel
 * @param table The parsed table object
 * @returns HTML string that can be copied to Excel
 */
export const tableToExcelHTML = (table: ParsedTable): string => {
  const { headers, rows } = table;
  
  if (headers.length === 0 || rows.length === 0) {
    return '';
  }

  // Create HTML table
  let html = '<table>';
  
  // Add header row
  html += '<thead><tr>';
  headers.forEach(header => {
    html += `<th>${header}</th>`;
  });
  html += '</tr></thead>';
  
  // Add data rows
  html += '<tbody>';
  rows.forEach(row => {
    html += '<tr>';
    headers.forEach(header => {
      html += `<td>${row[header] || ''}</td>`;
    });
    html += '</tr>';
  });
  html += '</tbody>';
  
  html += '</table>';
  
  return html;
};

/**
 * Convert a markdown table to HTML format suitable for copying to Excel
 * @param markdownTable The markdown table content
 * @returns HTML string that can be copied to Excel
 */
export const markdownTableToExcelHTML = (markdownTable: string): string => {
  const parsedTable = parseMarkdownTable(markdownTable);
  return tableToExcelHTML(parsedTable);
};

/**
 * Check if text contains a markdown table
 * @param text The text to check
 * @returns True if the text contains a markdown table
 */
export const containsMarkdownTable = (text: string): boolean => {
  // Simple detection: check for at least 2 table markers (|) and a separator line
  const lines = text.trim().split('\n');
  if (lines.length < 3) return false;

  // Check if first line has pipe characters
  const hasTableMarkers = lines[0].includes('|');

  // Check if second line looks like a separator line
  const isSeparatorLine = lines[1].match(/^\|?\s*[-:]+\s*\|/);

  return hasTableMarkers && !!isSeparatorLine;
};
