#!/usr/bin/env bash

# Steel Unit Converter - Build Script for Production Deployment
# This script builds the frontend on a development machine and prepares it for deployment

# Set script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
DIST_DIR="$FRONTEND_DIR/dist"
BUILD_DIR="$SCRIPT_DIR/production-build"
DEPLOY_PACKAGE="$SCRIPT_DIR/steel-unit-converter-deploy.tar.gz"

# Create build directory if it doesn't exist
mkdir -p "$BUILD_DIR"

# Function to print colored output
print_info() {
    echo -e "\033[0;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[0;32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[0;33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $1"
}

# Function to check if Node.js is installed
check_node() {
    print_info "Checking for Node.js..."
    if command -v node &> /dev/null; then
        print_success "Found Node.js: $(node --version)"
        return 0
    else
        print_error "Node.js not found. Please install Node.js 14 or higher."
        exit 1
    fi
}

# Function to build the frontend
build_frontend() {
    print_info "Building frontend for production deployment..."
    
    # Change directory to frontend
    cd "$FRONTEND_DIR" || exit
    
    # Install dependencies
    print_info "Installing frontend dependencies..."
    npm install
    
    # Create optimized vite.config.js for production
    print_info "Creating optimized vite.config.js for production..."
    cat > vite.config.js << 'EOF'
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    minify: true,
    sourcemap: false,
    target: 'es2015',
    cssCodeSplit: true,
    assetsInlineLimit: 4096,
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          material: ['@mui/material'],
          icons: ['@mui/icons-material'],
          emotion: ['@emotion/react', '@emotion/styled'],
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  }
});
EOF
    
    # Build frontend
    print_info "Building frontend..."
    npm run build
    
    # Check if build was successful
    if [ -d "$DIST_DIR" ] && [ -f "$DIST_DIR/index.html" ]; then
        print_success "Frontend built successfully."
        return 0
    else
        print_error "Frontend build failed."
        exit 1
    fi
}

# Function to prepare deployment package
prepare_deployment() {
    print_info "Preparing deployment package..."
    
    # Create necessary directories
    mkdir -p "$BUILD_DIR/frontend"
    mkdir -p "$BUILD_DIR/backend"
    mkdir -p "$BUILD_DIR/ssl"
    mkdir -p "$BUILD_DIR/logs"
    
    # Copy frontend dist
    print_info "Copying frontend dist..."
    cp -r "$DIST_DIR" "$BUILD_DIR/frontend/"
    
    # Copy backend files
    print_info "Copying backend files..."
    cp -r "$SCRIPT_DIR/backend"/* "$BUILD_DIR/backend/"
    
    # Copy deployment scripts
    print_info "Copying deployment scripts..."
    cp "$SCRIPT_DIR/start-built.sh" "$BUILD_DIR/"
    cp "$SCRIPT_DIR/LOW-MEMORY-DEPLOYMENT.md" "$BUILD_DIR/"
    
    # Create README file
    print_info "Creating README file..."
    cat > "$BUILD_DIR/README.md" << 'EOF'
# Steel Unit Converter - Production Deployment

This package contains the pre-built Steel Unit Converter application ready for deployment on a production server with limited memory (2GB).

## Quick Start

To start the application:

```bash
./start-built.sh
```

This will start the backend and frontend services without rebuilding the frontend.

## Commands

- Start: `./start-built.sh`
- Stop: `./start-built.sh stop`
- Status: `./start-built.sh status`
- Logs: `./start-built.sh logs`
- Setup URL Access (nginx): `./start-built.sh --url-access`

## Notes

- The frontend is pre-built and does not require rebuilding on the production server.
- The backend will still need to install its dependencies on first run.
- For more information, see the LOW-MEMORY-DEPLOYMENT.md file.
EOF
    
    # Create .env.production file for backend if it doesn't exist
    if [ ! -f "$BUILD_DIR/backend/.env.production" ]; then
        print_info "Creating sample .env.production file..."
        cat > "$BUILD_DIR/backend/.env.production" << 'EOF'
# Production Environment Configuration

# Database configuration - MySQL 8.0 RDS
RDS_HOSTNAME=your-rds-hostname
RDS_PORT=3306
RDS_DB_NAME=unit_converter
RDS_USERNAME=your-username
RDS_PASSWORD=your-password

# Database connection pool settings (optimized for MySQL 8.0 RDS)
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
SQL_ECHO=False    

# Security
SECRET_KEY=your-production-secret-key
CORS_ORIGINS=https://your-domain.com,http://localhost:3000

# Email settings
SMTP_SERVER=smtp.example.com
SMTP_PORT=25
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password

# Email rate limiting
EMAIL_RATE_LIMIT=10
EMAIL_RATE_LIMIT_PERIOD=3600
EOF
        print_warning "Please update the .env.production file with your actual production settings before deployment."
    fi
    
    # Create deployment package
    print_info "Creating deployment package..."
    cd "$SCRIPT_DIR" || exit
    tar -czf "$DEPLOY_PACKAGE" -C "$BUILD_DIR" .
    
    print_success "Deployment package created: $DEPLOY_PACKAGE"
    print_info "You can now transfer this package to your production server and extract it."
    print_info "Then run ./start-built.sh to start the application."
}

# Main function
main() {
    print_info "Starting build process for production deployment..."
    
    # Check requirements
    check_node
    
    # Build frontend
    build_frontend
    
    # Prepare deployment package
    prepare_deployment
    
    print_success "Build process completed successfully."
    print_info "Deployment package: $DEPLOY_PACKAGE"
}

# Run the main function
main
